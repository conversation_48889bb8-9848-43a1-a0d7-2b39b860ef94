import mssql from '@lcs/mssql-utility'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

// Gets all users that are instructors for a session of any learning context
export default async function isUserEnrolled (contextId: string, userId: string): Promise<boolean> {
  const request = mssql.getPool().request()
  request.input('contextId', contextId)
  request.input('userId', userId)

  const results = await request.query<{ Count: number }>(`
    SELECT COUNT(*) AS Count
    FROM [${SessionEnrollmentsTableName}]
    WHERE [${SessionEnrollmentFields.SessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextId
    )
    AND [${SessionEnrollmentFields.UserID}] = @userId
  `)

  return results.recordset[0].Count > 0
}
