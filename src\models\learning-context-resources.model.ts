import { Table } from '@lcs/mssql-utility'
import { LearningContextResourcesTableName, LearningContextResourcesFields, LearningContextResources } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'


export default class LearningContextResourcesModel extends Table<LearningContextResources, LearningContextResources> {
  public fields: LearningContextResources

  constructor (fields?: LearningContextResources) {
    super(LearningContextResourcesTableName, [
        LearningContextResourcesFields.LearningObjectID,
        LearningContextResourcesFields.LearningContextID,
        LearningContextResourcesFields.CreatedBy
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  importFromDatabase (record: LearningContextResources): void {
    this.fields.LearningObjectID = record.LearningObjectID
    this.fields.LearningContextID = record.LearningContextID
    this.fields.StudentAccessible = record.StudentAccessible
    this.fields.CreatedBy = record.CreatedBy
    this.fields.CreatedOn = record.CreatedOn
    this.fields.ModifiedBy = record.ModifiedBy
    this.fields.ModifiedOn = record.ModifiedOn
  }

  exportJsonToDatabase (): LearningContextResources {
    return {
        LearningObjectID: this.fields.LearningObjectID,
        LearningContextID: this.fields.LearningContextID,
        StudentAccessible: this.fields.StudentAccessible,
        CreatedBy: this.fields.CreatedBy,
        CreatedOn: this.fields.CreatedOn,
        ModifiedBy: this.fields.ModifiedBy,
        ModifiedOn: this.fields.ModifiedOn
    }
  }
}

