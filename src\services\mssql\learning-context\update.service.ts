import mssql, { addRow, DB_Errors as dbErrors, deleteRow, updateRow } from '@lcs/mssql-utility'
import CoursePrerequisiteModel from '../../../models/course-prerequisite.model.js'
import LearningContextKeywordModel from '../../../models/learning-context-keyword.model.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import errors from '../../../config/errors.js'
import { Transaction } from 'mssql'
import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { CoursePrerequisite, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import { LearningContextKeyword, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import getObjectiveIdsForLearningContext from '../learning-context-objectives/get-multi.service.js'
import createLearningContextObjective from '../learning-context-objectives/create.service.js'
import LearningContextObjectiveModel from '../../../models/learning-context-objective.model.js'
import removeLearningContextObjective from '../learning-context-objectives/remove.service.js'
import updateLearningContextObjective from '../learning-context-objectives/update.service.js'
import { CoursePrerequisiteJson } from '@tess-f/lms/dist/common/course-prerequisite.js'
import { CoursePrerequisiteAlternativeModel } from '../../../models/course-prerequisite-alternative.model.js'
import { CoursePrerequisiteAlternative } from '@tess-f/sql-tables/dist/lms/course-prerequisite-alternative.js'
import LearningContextResourcesModel from '../../../models/learning-context-resources.model.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import getResourcesByLearningContextID from '../learning-context-resources/get.service.js'
import createLearningContextResource from '../learning-context-resources/create.service.js'
import removeLearningContextResource from '../learning-context-resources/delete.service.js'
import modifyLearningContextResource from '../learning-context-resources/modify.service.js'
import getLearningObject from '../learning-objects/get.service.js'
import { LearningContextResources } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'
import { getErrorMessage } from '@tess-f/backend-utils'
/*
    ID must be set in the learning context
*/
export default async function (learningContext: LearningContextModel): Promise<LearningContextModel> {
  if (!learningContext.fields.ID) {
    throw new Error(errors.INVALID_ARGS)
  }
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    const updatedRecord = await updateRow<LearningContext>(transaction.request(), learningContext, { ID: learningContext.fields.ID })
    const updated = new LearningContextModel(undefined, updatedRecord[0])
    updated.fields.Prerequisites = await replacePrerequisites(transaction, updated.fields.ID!, learningContext.fields.Prerequisites)
    updated.fields.Keywords = await replaceKeywords(transaction, updated.fields.ID!, learningContext.fields.Keywords)
    updated.fields.ObjectiveIds = await updateObjectives(transaction, updated.fields, learningContext.fields.ObjectiveIds)
    updated.fields.Resources = await updateResources(transaction, updated.fields, learningContext.fields.Resources)

    await transaction.commit()

    // create an activity stream record
    const activity = new ActivityStream({
      UserID: updated.fields.ModifiedBy,
      LinkText: updated.fields.Label + ': ' + updated.fields.Title,
      LinkID: updated.fields.ID,
      ActivityID: Activities.ModifiedContext,
      CreatedOn: new Date()
    })
    createActivity(activity)

    return updated
  } catch (err) {
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function replacePrerequisites (transaction: Transaction, contextID: string, prerequisites?: CoursePrerequisiteJson[]): Promise<CoursePrerequisiteJson[] | undefined> {
  // First delete all
  await deleteRow(transaction.request(), CoursePrerequisitesTableName, { CourseID: contextID })

  if (prerequisites) {
    const prereqs: CoursePrerequisiteJson[] = []
    for (const prerequisite of prerequisites) {
      const prereq = new CoursePrerequisiteModel(prerequisite)
      prereq.fields.CourseID = contextID
      prereq.fields.ID = undefined
      const record = await addRow<CoursePrerequisite>(transaction.request(), prereq)
      prereq.importFromDatabase(record)

      // if the prerequisite is enforced and has alternatives, add those as well
      if (prereq.fields.Enforce && prerequisite.Alternatives.length > 0) {
        for (const alternative of prerequisite.Alternatives) {
          const alt = new CoursePrerequisiteAlternativeModel(alternative)
          alt.fields.ForPrerequisiteId = prereq.fields.ID
          const newAlt = await addRow<CoursePrerequisiteAlternative>(transaction.request(), alt)
          prereq.fields.Alternatives.push(newAlt)
        }
      }
      prereqs.push(prereq.fields)
    }
    return prereqs
  } else {
    return undefined
  }
}

async function replaceKeywords (transaction: Transaction, contextID: string, keywords?: string[]): Promise<string[] | undefined> {
  // delete keywords first
  await deleteRow(transaction.request(), LearningContextKeywordsTableName, { LearningContextID: contextID })

  if (keywords) {
    // create new keywords
    const output: string[] = []

    for (const word of keywords) {
      const keyword = new LearningContextKeywordModel({
        Keyword: word,
        LearningContextID: contextID
      })
      const record = await addRow<LearningContextKeyword>(transaction.request(), keyword)
      output.push(record.Keyword!)
    }

    return output
  } else {
    return undefined
  }
}

async function updateObjectives (transaction: Transaction, context: LearningContext, objectiveIds?: string[]): Promise<string[]> {
  // get the original list of objectives
  const original = await getObjectiveIdsForLearningContext(context.ID ?? '')

  if (objectiveIds === undefined) {
    // if no list was passed in, send back the original
    return original
  }

  // find the objectives to remove, add, update
  const toAdd = objectiveIds.filter(newObjective => !original.includes(newObjective))
  const toRemove = original.filter(originalObjective => !objectiveIds.includes(originalObjective))
  const toUpdate = objectiveIds.filter((id, index) => original.includes(id) && original.indexOf(id) !== index)

  await Promise.all(toAdd.map(async id => {
    await createLearningContextObjective(
      new LearningContextObjectiveModel({ LearningContextId: context.ID, ObjectiveId: id, OrderId: objectiveIds.indexOf(id) + 1 }),
      context.Title ?? 'Course',
      context.Label ?? 'Course',
      context.ContextTypeID ?? -1,
      transaction.request()
    )
  }))
  

  await Promise.all(toRemove.map(async id => {
    await removeLearningContextObjective(context.ID ?? '', id, transaction.request())
  }))

  await Promise.all(toUpdate.map(async id => {
    await updateLearningContextObjective(new LearningContextObjectiveModel({ LearningContextId: context.ID, ObjectiveId: id, OrderId: objectiveIds.indexOf(id) + 1 }), transaction.request())
  }))

  return objectiveIds
}

async function updateResources (transaction: Transaction, context: LearningContext, resources?: LearningObjectJson[]): Promise<LearningObjectJson[]> {
  // get the original list of resources
  let original: LearningContextResources[] = []
  try {
    original = await getResourcesByLearningContextID(context.ID ?? '')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }
  
  if (resources === undefined) {
    // if no list was passed in, send back an the original learning objects
    return Promise.all(original.map(async resource => {
      const lo = await getLearningObject(resource.LearningObjectID ?? '')
      lo.fields.StudentAccessible = resource.StudentAccessible
      return lo.fields
    }))
  }
  
  // find the objectives to remove, add, update
  const toAdd = resources.filter(newResource => !original.some(existing => existing.LearningObjectID === newResource.ID))
  const toRemove = original.filter(originalResource => !resources.some(newResource => newResource.ID === originalResource.LearningObjectID))
  const toUpdate = resources.filter(newResource => original.some(existing => existing.LearningObjectID === newResource.ID && existing.StudentAccessible !== newResource.StudentAccessible))

  for (const resource of toAdd) {
    await createLearningContextResource(
      new LearningContextResourcesModel({
        LearningContextID: context.ID,
        LearningObjectID: resource.ID,
        StudentAccessible: resource.StudentAccessible,
        CreatedBy: context.ModifiedBy,
        CreatedOn: new Date()
      }),
      transaction.request()
    )
  }

  for (const resource of toRemove) {
    await removeLearningContextResource(resource.LearningObjectID ?? '', context.ID ?? '', transaction.request())
  }

  for (const resource of toUpdate) {
    await modifyLearningContextResource(new LearningContextResourcesModel({ LearningContextID: context.ID, LearningObjectID: resource.ID, StudentAccessible: resource.StudentAccessible, ModifiedBy: context.ModifiedBy, ModifiedOn: new Date() }), transaction.request())
  }

  return await Promise.all(resources.map(async resource => {
    const lo = await getLearningObject(resource.ID ?? '', undefined, undefined)
    lo.fields.StudentAccessible = resource.StudentAccessible
    return lo.fields
  }))
}