import { expect } from 'chai'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import controller from './get.controller.js'

describe('HTTP get controller - Integration Tests', () => {
    before(() => logger.init({ level: 'error' }))

    // You'll need real IDs from your database for these tests
    const REAL_CONTEXT_ID = 'D7062EB9-7646-4017-9A0D-FFE39FF9EBFD' // Replace with actual context ID
    const REAL_USER_ID = '72EDDFA3-A8FA-4025-A3A2-903C8222661B' // Replace with actual user ID
    const REAL_INSTRUCTOR_ID = 'INSTRUCTOR-USER-ID' // Replace with actual instructor ID
    const REAL_ENROLLED_USER_ID = 'ENROLLED-USER-ID' // Replace with actual enrolled user ID

    it('should return empty resources for non-enrolled, non-instructor user viewing course', async () => {
        const mocks = httpMocks.createMocks({
            session: {
                userId: REAL_USER_ID // User who is NOT enrolled and NOT instructor
            },
            params: {
                id: REAL_CONTEXT_ID
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'true', // This is the key - user is viewing
                rating: 'false',
                upcomingSessionCount: 'false'
            }
        })
        mocks.req.claims = [] // No special claims

        await controller(mocks.req, mocks.res)
        
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        const responseData = mocks.res._getJSONData()
        expect(responseData.Resources).to.be.an('array')
        expect(responseData.Resources).to.have.length(0) // Should be empty
    })

    it('should return filtered resources for enrolled student viewing course', async () => {
        const mocks = httpMocks.createMocks({
            session: {
                userId: REAL_ENROLLED_USER_ID // User who IS enrolled but NOT instructor
            },
            params: {
                id: REAL_CONTEXT_ID
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'true',
                rating: 'false',
                upcomingSessionCount: 'false'
            }
        })
        mocks.req.claims = []

        await controller(mocks.req, mocks.res)
        
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        const responseData = mocks.res._getJSONData()
        expect(responseData.Resources).to.be.an('array')
        // Should only contain student-accessible resources
        if (responseData.Resources.length > 0) {
            responseData.Resources.forEach((resource: any) => {
                expect(resource.StudentAccessible).to.be.true
            })
        }
    })

    it('should return all resources for instructor viewing course', async () => {
        const mocks = httpMocks.createMocks({
            session: {
                userId: REAL_INSTRUCTOR_ID // User who IS instructor
            },
            params: {
                id: REAL_CONTEXT_ID
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'true',
                rating: 'false',
                upcomingSessionCount: 'false'
            }
        })
        mocks.req.claims = []

        await controller(mocks.req, mocks.res)
        
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        const responseData = mocks.res._getJSONData()
        expect(responseData.Resources).to.be.an('array')
        // Instructor should see all resources (no filtering)
    })
})
