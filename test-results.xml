<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="4.295" tests="4" failures="0">
  <testsuite name="Root Suite" timestamp="2025-08-14T21:41:36" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get controller" timestamp="2025-08-14T21:41:36" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\controllers\http\learning-contexts\get.controller.spec.ts" time="4.285" failures="0">
    <testcase name="HTTP get controller returns success if the request data is valid" time="3.503" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get controller returns success if the request data is valid in view" time="0.150" classname="returns success if the request data is valid in view">
    </testcase>
    <testcase name="HTTP get controller returns an error if the request data is invalid" time="0.263" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get controller returns an internal server error if the request is rejected" time="0.272" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
</testsuites>