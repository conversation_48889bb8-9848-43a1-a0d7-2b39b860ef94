<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="5.972" tests="4" failures="0">
  <testsuite name="Root Suite" timestamp="2025-08-18T14:50:35" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get controller" timestamp="2025-08-18T14:50:35" tests="4" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\controllers\http\learning-contexts\get.controller.spec.ts" time="5.946" failures="0">
    <testcase name="HTTP get controller returns success if the request data is valid" time="5.232" classname="returns success if the request data is valid">
    </testcase>
    <testcase name="HTTP get controller returns success if the request data is valid in view" time="0.197" classname="returns success if the request data is valid in view">
    </testcase>
    <testcase name="HTTP get controller returns an error if the request data is invalid" time="0.193" classname="returns an error if the request data is invalid">
    </testcase>
    <testcase name="HTTP get controller returns an internal server error if the request is rejected" time="0.170" classname="returns an internal server error if the request is rejected">
    </testcase>
  </testsuite>
</testsuites>