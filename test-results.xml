<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="0.193" tests="3" failures="3">
  <testsuite name="Root Suite" timestamp="2025-08-18T15:35:46" tests="0" time="0.000" failures="0">
  </testsuite>
  <testsuite name="HTTP get controller - Integration Tests" timestamp="2025-08-18T15:35:46" tests="3" file="C:\Users\<USER>\.local\Dev\TMS\TESS-LMS-Server\src\controllers\http\learning-contexts\get.controller.integration.spec.ts" time="0.162" failures="3">
    <testcase name="HTTP get controller - Integration Tests should return empty resources for non-enrolled, non-instructor user viewing course" time="0.047" classname="should return empty resources for non-enrolled, non-instructor user viewing course">
      <failure message="expected 500 to equal 200" type="AssertionError"><![CDATA[AssertionError: expected 500 to equal 200
    at Context.<anonymous> (file:///C:/Users/<USER>/.local/Dev/TMS/TESS-LMS-Server/src/controllers/http/learning-contexts/get.controller.integration.spec.ts:30:38)

      + expected - actual

      -500
      +200
      ]]></failure>
    </testcase>
    <testcase name="HTTP get controller - Integration Tests should return filtered resources for enrolled student viewing course" time="0.009" classname="should return filtered resources for enrolled student viewing course">
      <failure message="expected 500 to equal 200" type="AssertionError"><![CDATA[AssertionError: expected 500 to equal 200
    at Context.<anonymous> (file:///C:/Users/<USER>/.local/Dev/TMS/TESS-LMS-Server/src/controllers/http/learning-contexts/get.controller.integration.spec.ts:53:38)

      + expected - actual

      -500
      +200
      ]]></failure>
    </testcase>
    <testcase name="HTTP get controller - Integration Tests should return all resources for instructor viewing course" time="0.009" classname="should return all resources for instructor viewing course">
      <failure message="expected 500 to equal 200" type="AssertionError"><![CDATA[AssertionError: expected 500 to equal 200
    at Context.<anonymous> (file:///C:/Users/<USER>/.local/Dev/TMS/TESS-LMS-Server/src/controllers/http/learning-contexts/get.controller.integration.spec.ts:81:38)

      + expected - actual

      -500
      +200
      ]]></failure>
    </testcase>
  </testsuite>
</testsuites>