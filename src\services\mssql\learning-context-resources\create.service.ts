import mssql, { addRow } from '@lcs/mssql-utility'
import LearningContextResourcesModel from '../../../models/learning-context-resources.model.js'
import { LearningContextResources } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'
import { Request } from 'mssql'

export default async function createLearningContextResource (model: LearningContextResourcesModel, request?: Request): Promise<LearningContextResourcesModel> {
  request ??= mssql.getPool().request();
  const record = await addRow<LearningContextResources>(request, model)
  return new LearningContextResourcesModel(record)
}
