import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getAvailableCoursePrereqsController from './get-available-course-prereqs.controller.js'
import getCompletionController from './get-completion.controller.js'
import getContextsWithoutUpcomingSessionsController from './get-contexts-without-upcoming-sessions.controller.js'
import getDurationController from './get-duration.controller.js'
import getIltContextWithoutSessionForAssignmentController from './get-ilt-context-without-session-for-assignment.controller.js'
import getPaginatedController from './get-paginated.controller.js'
import getParentIdsController from './get-parent-ids.controller.js'
import getController from './get.controller.js'
import isCourseIdUniqueController from './is-course-id-unique.controller.js'
import updateController from './update.controller.js'
import getContextLabelsController from './get-labels.controller.js'
import getNextLearningObjectIdForContextController from './get-next-learning-object-id-for-context.controller.js'
import isContextExamLockedController from './is-context-exam-locked.controller.js'
import getContextsThatUseObjectController from './get-contexts-that-use-object.controller.js'
import getParentsForContextController from './get-parents-for-context.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/learning-context/:id', getController as RequestHandler)
router.put('/learning-context/:id', checkClaims([Claims.MODIFY_CATALOG_ITEMS, Claims.MODIFY_COURSE, Claims.CREATE_CATALOG_ITEMS, Claims.CREATE_COURSE]), updateController as RequestHandler)
router.delete('/learning-context/:id', checkClaims([Claims.DELETE_CATALOG_ITEMS, Claims.DELETE_COURSE]), deleteController as RequestHandler)
router.post('/learning-context', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.CREATE_COURSE]), createController as RequestHandler)
router.get('/context-duration/:id', getDurationController as RequestHandler)
router.get('/context-completion/:contextID/:userID', getCompletionController as RequestHandler)
router.get('/is-context-exam-locked/:contextID/:userID', isContextExamLockedController as RequestHandler)
router.post('/course-id-unique', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), isCourseIdUniqueController as RequestHandler)
router.post('/paginated-contexts', checkClaims([Claims.VIEW_COURSES, Claims.CREATE_COURSE, Claims.MODIFY_COURSE, Claims.DELETE_COURSE]), getPaginatedController as RequestHandler)
router.get('/available-course-prereqs', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.CREATE_COURSE, Claims.MODIFY_CATALOG_ITEMS, Claims.MODIFY_COURSE]), getAvailableCoursePrereqsController as RequestHandler)
router.get('/ilt-context-without-upcoming-sessions/:id', getContextsWithoutUpcomingSessionsController as RequestHandler)
router.get('/assignment-ilt-contexts-without-sessions/:id', getIltContextWithoutSessionForAssignmentController as RequestHandler)
router.get('/parent-context-ids/:id', getParentIdsController as RequestHandler)
router.get('/context-labels', getContextLabelsController as RequestHandler)
router.get('/start-context/:id', getNextLearningObjectIdForContextController as RequestHandler)
router.get('/contexts-that-use-object/:id', checkClaims([Claims.VIEW_COURSES, Claims.VIEW_CATALOG, Claims.VIEW_FILES]), getContextsThatUseObjectController as RequestHandler)
router.get('/parent-contexts/:id', checkClaims([Claims.VIEW_COURSES, Claims.VIEW_CATALOG]), getParentsForContextController as RequestHandler)

export default router