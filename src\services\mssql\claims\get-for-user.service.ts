import mssql from '@lcs/mssql-utility'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { GroupClaim, GroupClaimFields, GroupClaimsTableName } from '@tess-f/sql-tables/dist/lms/group-claim.js'

export default async function getClaimsForUser (userId: string) : Promise<string[]> {
  const request = mssql.getPool().request()
  request.input('UserID', userId)

  const result = await request.query<Required<Pick<GroupClaim, 'Claim'>>>(`
    SELECT DISTINCT [${GroupClaimFields.Claim}]
    FROM [${GroupClaimsTableName}]
    WHERE [${GroupClaimFields.GroupID}] IN (
      SELECT [${UserGroupFields.GroupID}]
      FROM [${UserGroupTableName}]
      WHERE [${UserGroupFields.UserID}] = @UserID
    )
  `)

  return result.recordset.map(record => record.Claim!)
}
