import mssql from '@lcs/mssql-utility'
import { LearningContextSessionInstructorFields, LearningContextSessionInstructorsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session-instructor.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'

// Gets all users that are instructors for a session of any learning context
export default async function isInstructor (contextId: string, userId: string): Promise<boolean> {
  const request = mssql.getPool().request()
  request.input('contextId', contextId)
  request.input('userId', userId)

  const results = await request.query<{ Count: number }>(`
    SELECT COUNT(*) AS Count
    FROM [${LearningContextSessionInstructorsTableName}]
    WHERE [${LearningContextSessionInstructorFields.SessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextId
    )
    AND [${LearningContextSessionInstructorFields.UserID}] = @userId
  `)

  return results.recordset[0].Count > 0
}
