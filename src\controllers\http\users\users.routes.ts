import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from 'express'
import getContextAssignedUsersController from './get-context-assigned-users.controller.js'
import getContextCompletedUsersController from './get-context-completed-users.controller.js'
import getContextCreatorsController from './get-context-creators.controller.js'
import getContextFavoritesController from './get-context-favorites.controller.js'
import getContextInProgressUsersController from './get-context-in-progress-users.controller.js'
import getContextModifiersController from './get-context-modifiers.controller.js'
import getContextPlanUsersController from './get-context-plan-users.controller.js'
import getObjectAssignedUsersController from './get-object-assigned-users.controller.js'
import getObjectCompletedUsersController from './get-object-completed-users.controller.js'
import getObjectCreatorsController from './get-object-creators.controller.js'
import getObjectFavoritesController from './get-object-favorites.controller.js'
import getObjectInProgressUsersController from './get-object-in-progress-users.controller.js'
import getObjectModifiersController from './get-object-modifiers.controller.js'
import getObjectPlanUsersController from './get-object-plan-users.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/context-creators', checkClaims([Claims.VIEW_COURSES, Claims.CREATE_COURSE, Claims.MODIFY_COURSE, Claims.DELETE_COURSE]), getContextCreatorsController as RequestHandler)
router.get('/context-modifiers', checkClaims([Claims.VIEW_COURSES, Claims.CREATE_COURSE, Claims.MODIFY_COURSE, Claims.DELETE_COURSE]), getContextModifiersController as RequestHandler)
router.get('/object-creators', checkClaims([Claims.VIEW_FILES, Claims.CREATE_FILE, Claims.MODIFY_FILE, Claims.DELETE_FILE]), getObjectCreatorsController as RequestHandler)
router.get('/object-modifiers', checkClaims([Claims.VIEW_FILES, Claims.CREATE_FILE, Claims.MODIFY_FILE, Claims.DELETE_FILE]), getObjectModifiersController as RequestHandler)

// Content Dashboard Routes
router.post('/get-in-progress-context-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextInProgressUsersController as RequestHandler)
router.post('/get-in-progress-object-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getObjectInProgressUsersController as RequestHandler)
router.post('/get-context-favorite-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextFavoritesController as RequestHandler)
router.post('/get-object-favorite-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getObjectFavoritesController as RequestHandler)
router.post('/get-context-plan-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextPlanUsersController as RequestHandler)
router.post('/get-object-plan-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getObjectPlanUsersController as RequestHandler)
router.post('/get-completed-context-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextCompletedUsersController as RequestHandler)
router.post('/get-completed-object-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getObjectCompletedUsersController as RequestHandler)
router.post('/get-context-assigned-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextAssignedUsersController as RequestHandler)
router.post('/get-object-assigned-users/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getObjectAssignedUsersController as RequestHandler)

export default router
