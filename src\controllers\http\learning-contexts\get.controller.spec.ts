import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())

    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../mappers/learning-context-extras.mapper.js': {
                default: Sinon.stub().returns(Promise.resolve(null))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context/get-instructors.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            },
            '../../../services/mssql/learning-context/get-enrolled.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            }

        })

        const mocks = httpMocks.createMocks({

            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'false',
                rating: 'false',
                upcomingSessionCount: 'false'
            }

        })
        mocks.req.claims = []
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    //
    it('returns success if the request data is valid in view', async () => {
        const controller = await esmock('./get.controller', {
            '../../../mappers/learning-context-extras.mapper.js': {
                default: Sinon.stub().returns(Promise.resolve(null))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context/get-instructors.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            },
            '../../../services/mssql/learning-context/get-enrolled.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            }

        })

        const mocks = httpMocks.createMocks({

            session: {
                userId: '72EDDFA3-A8FA-4025-A3A2-903C8222661B'
            },
            params: {
                id: '427D0125-42DE-4818-8A52-2C552B7544B2'
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'true',
                rating: 'false',
                upcomingSessionCount: 'false'
            }

        })
        mocks.req.claims = []
        await controller(mocks.req, mocks.res)
        console.log(mocks.res._getData())
        
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    //

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../mappers/learning-context-extras.mapper.js': {
                default: Sinon.stub().returns(Promise.resolve(null))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context/get-instructors.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            },
            '../../../services/mssql/learning-context/get-enrolled.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            }

        })

        const mocks = httpMocks.createMocks({

            session: {
                userId: uuid()
            },
            params: {
                id: false
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'false',
                rating: 'false',
                upcomingSessionCount: 'false'
            }

        })
        mocks.req.claims = []
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data:')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../mappers/learning-context-extras.mapper.js': {
                default: Sinon.stub().returns(Promise.resolve(null))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context/get-instructors.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            },
            '../../../services/mssql/learning-context/get-enrolled.service.js': {
                default: Sinon.stub().returns(Promise.resolve(false))
            }

        })

        const mocks = httpMocks.createMocks({

            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            query: {
                prerequisites: 'false',
                nestedContexts: 'false',
                incViews: 'false',
                rating: 'false',
                upcomingSessionCount: 'false'
            }

        })
        mocks.req.claims = []
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })


   

})