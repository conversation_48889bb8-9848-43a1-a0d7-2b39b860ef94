import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningContextResources, LearningContextResourcesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'
import { Request } from 'mssql'

export default async function removeLearningContextResource (LearningObjectID: string, LearningContextID: string, request?: Request) {
  request ??= mssql.getPool().request();
  await deleteRow<LearningContextResources>(request, LearningContextResourcesTableName, { LearningObjectID: LearningObjectID, LearningContextID: LearningContextID })
}