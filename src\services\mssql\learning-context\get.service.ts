import logger from '@lcs/logger'
import mssqlUtils, { addRow, DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import { Request } from 'mssql'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import CoursePrerequisiteModel from '../../../models/course-prerequisite.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { GetNestedContexts } from './utils.service.js'
import createActivity from '../activity-stream/create.service.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { CoursePrerequisite, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import { LearningContextUserView, LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'
import { LearningContextKeyword, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextRatingFields, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'
import LearningContextUserViewModel from '../../../models/learning-context-user-view.model.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import getAlternativesForPrerequisite from '../course-prerequisite-alternative/get-for-prerequisite.service.js'
import getResourcesByLearningContextID from '../learning-context-resources/get.service.js'
import { LearningContextResources } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'
import getLearningObject from '../learning-objects/get.service.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'

const log = logger.create('Service-MSSQL.get-learning-context')

interface GetLearningContextOptions {
  rating?: boolean
  prerequisites?: boolean
  nestedContexts?: boolean
  incViews?: boolean
  upcomingSessionCount?: boolean
  resources?: boolean
}

export default async function (learningContextId: string, UserID: string | undefined, options: GetLearningContextOptions = {}, showViews: boolean = true): Promise<LearningContextModel> {
  const pool = mssqlUtils.getPool()
  const context = await getLearningContext(pool.request(), learningContextId)

  const extras = await Promise.all([
    options.rating ? getAverageRating(pool.request(), learningContextId) : undefined,
    options.prerequisites ? getPrerequisites(pool.request(), learningContextId) : undefined,
    options.nestedContexts ? GetNestedContexts(pool.request(), learningContextId) : [],
    options.incViews && UserID ? incrementViewCount(pool.request(), learningContextId, UserID) : undefined,
    options.upcomingSessionCount ? getUpcomingSessionCount(pool.request(), learningContextId) : undefined,
    options.resources ? getResources(learningContextId) : undefined
  ])

  if (options.incViews && UserID) {
    // create activity stream record
    const activity = new ActivityStreamModel({
      UserID,
      LinkText: context.fields.Label + ': ' + context.fields.Title,
      LinkID: learningContextId,
      ActivityID: Activities.ViewedContext,
      CreatedOn: new Date()
    })
    await createActivity(activity)
  }

  context.fields.Rating = extras[0]?.average ? extras[0].average : 0
  context.fields.RatingCount = extras[0]?.count ? extras[0].count : 0
  context.fields.Prerequisites = extras[1]?.map(record => record.fields)
  context.fields.Contexts = extras[2]
  context.fields.Views = showViews ? await getViewCount(pool.request(), learningContextId) : undefined
  context.fields.Keywords = await getKeywords(pool.request(), learningContextId)
  context.fields.UpcomingSessionCount = extras[4]
  context.fields.Resources = extras[5]


  return context
}

async function getLearningContext(request: Request, ID: string): Promise<LearningContextModel> {
  const record = (await getRows<LearningContext>(
    LearningContextTableName,
    request,
    { ID }
  ))[0]
  return new LearningContextModel(undefined, record)
}

async function getAverageRating(request: Request, ID: string): Promise<{ average: number, count: number }> {
  try {
    const query = `
      SELECT AVG([${LearningContextRatingFields.Rating}]) as average, Count([${LearningContextRatingFields.ID}]) as count
      FROM [${LearningContextRatingsTableName}]
      WHERE [${LearningContextRatingFields.LearningContextID}] = @id
    `
    request.input('id', ID)
    const res = await request.query<{ average: any, count: any }>(query)
    return res.recordset[0]
  } catch (error) {
    log('error', 'Unexpected database error: getting learning context average rating', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

async function getPrerequisites(request: Request, ID: string): Promise<CoursePrerequisiteModel[]> {
  try {
    const records = await getRows<CoursePrerequisite>(CoursePrerequisitesTableName, request, { CourseID: ID })
    const prerequisites = await Promise.all(records.map(async record => {
      const prereq = new CoursePrerequisiteModel()
      prereq.importFromDatabase(record)
      prereq.fields.Alternatives = await getAlternativesForPrerequisite(prereq.fields.ID ?? '')
      return prereq
    }))

    return prerequisites
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    log('error', 'Unknown Database Error: get course prerequisites', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

async function incrementViewCount(request: Request, contextID: string, userID: string) {
  const userView = new LearningContextUserViewModel({
    UserID: userID,
    LearningContextID: contextID,
    CreatedOn: new Date()
  })
  await addRow<LearningContextUserView>(request, userView)
}

async function getViewCount(request: Request, contextID: string): Promise<number> {
  try {
    request.input('contextID', contextID)
    const query = `
      SELECT COUNT([${LearningContextUserViewFields.LearningContextID}]) AS Views
      FROM [${LearningContextUserViewsTableName}]
      WHERE [${LearningContextUserViewFields.LearningContextID}] = @contextID
    `
    const res = await request.query<{ Views: number }>(query)
    if (res.recordset.length === 0) return 0
    else return res.recordset[0].Views
  } catch (error) {
    log('error', 'Unexpected database error: getting learning context view count', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

async function getKeywords(request: Request, contextID: string): Promise<string[]> {
  try {
    const keys = await getRows<LearningContextKeyword>(LearningContextKeywordsTableName, request, { LearningContextID: contextID })
    return keys.map(key => key.Keyword!)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    } else {
      log('error', 'Unexpected database error: getting learning context keywords', { errorMessage: getErrorMessage(error), success: false })
      throw error
    }
  }
}

async function getUpcomingSessionCount(request: Request, contextID: string): Promise<number> {
  try {
    request.input('contextID', contextID)
    const res = await request.query<{ SessionCount: number }>(`
      SELECT COUNT(*) AS SessionCount
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
      AND [${LearningContextSessionFields.StartDate}] >= GETDATE()
      AND [${LearningContextSessionFields.SessionStatusID}] = ${SessionStatuses.Open}
    `)
    return res.recordset.length > 0 ? res.recordset[0].SessionCount : 0
  } catch (error) {
    log('error', 'Unexpected database error: getting upcoming session count for learning context', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}


async function getResources(LearningContextID: string): Promise<LearningObjectJson[]> {
  try {
    const resources = await getResourcesByLearningContextID(LearningContextID ?? '')

    return Promise.all(resources.map(async resource => {
      const lo = await getLearningObject(resource.LearningObjectID ?? '')
      lo.fields.StudentAccessible = resource.StudentAccessible
      return lo.fields
    }))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    log('error', 'Unexpected database error: getting learning context resources', { errorMessage, success: false })
    throw error
  }
}
