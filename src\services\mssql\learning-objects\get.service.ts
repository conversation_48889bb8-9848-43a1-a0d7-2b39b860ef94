import mssql, { addRow, getRows, DB_Errors } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectUserViewModel from '../../../models/learning-object-user-view.model.js'
import { ConnectionPool, Request, Transaction } from 'mssql'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectUserView, LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'
import { LearningObjectKeyword, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import getObjectiveIdsForLearningObject from '../learning-object-objectives/get-multi.service.js'

export default async function (learningObjectId: string, userID: string | undefined = undefined, options: { incViews?: boolean, contextCount?: boolean, forContextID?: string, rating?: boolean } = {}, connection?: ConnectionPool | Transaction): Promise<LearningObjectModel> {
  connection ??= mssql.getPool()
  const learningObject = await getLearningObject(connection.request(), learningObjectId)

  if (options.incViews && userID) {
    await incrementViewCount(connection.request(), learningObjectId, userID)
    const activity = new ActivityStreamModel({
      UserID: userID,
      LinkText: learningObject.fields.Title,
      LinkID: learningObjectId,
      ActivityID: Activities.ViewedLearningObject,
      CreatedOn: new Date()
    })
    await createActivity(activity)
  }

  const extras = await Promise.all([
    options.rating ? getAverageRating(connection.request(), learningObjectId) : undefined,
    options.contextCount ? getContextCount(connection.request(), learningObjectId) : undefined,
    options.forContextID ? getOrderID(connection.request(), learningObjectId, options.forContextID) : undefined,
    getKeywordsForObject(connection.request(), learningObjectId),
    getViewCounts(connection.request(), learningObjectId),
    getObjectiveIdsForLearningObject(learningObjectId)
  ])

  learningObject.fields.Keywords = extras[3]
  learningObject.fields.ContextCount = extras[1]
  learningObject.fields.OrderID = extras[2]
  learningObject.fields.Rating = extras[0]?.average ? extras[0].average : 0
  learningObject.fields.RatingCount = extras[0]?.count ? extras[0].count : 0
  learningObject.fields.Views = extras[4]
  learningObject.fields.ObjectiveIds = extras[5]

  return learningObject
}

async function getLearningObject (request: Request, objectID: string): Promise<LearningObjectModel> {
  const records = await getRows<LearningObject>(
    LearningObjectsTableName,
    request,
    { ID: objectID }
  )
  return new LearningObjectModel(undefined, records[0])
}

async function incrementViewCount (request: Request, objectID: string, userID: string): Promise<void> {
  const userView = new LearningObjectUserViewModel({
    UserID: userID,
    LearningObjectID: objectID,
    CreatedOn: new Date()
  })
  await addRow<LearningObjectUserView>(request, userView)
}

export async function getKeywordsForObject (request: Request, objectID: string): Promise<string[]> {
  try {
    const keys = await getRows<LearningObjectKeyword>(LearningObjectKeywordsTableName, request, { LearningObjectID: objectID })
    return keys.map(key => key.Keyword!)
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      return []
    } else {
      throw error
    }
  }
}

async function getViewCounts (request: Request, objectID: string): Promise<number> {
  request.input('objectID', objectID)
  const res = await request.query(`
    SELECT *
    FROM [${LearningObjectUserViewsTableName}]
    WHERE [${LearningObjectUserViewFields.LearningObjectID}] = @objectID
  `)
  return res.recordset.length
}

async function getAverageRating (request: Request, id: string): Promise<{ average: number, count: number }> {
  request.input('id', id)
  const res = await request.query<{ average: number, count: number }>(`
    SELECT AVG([${LearningObjectRatingFields.Rating}]) as average, Count([${LearningObjectRatingFields.ID}]) as count
    FROM [${LearningObjectRatingsTableName}]
    WHERE [${LearningObjectRatingFields.LearningObjectID}] = @id
  `)
  return res.recordset[0]
}

async function getOrderID (request: Request, objectID: string, contextID: string): Promise<number | undefined> {
  request.input('objectID', objectID)
  request.input('contextID', contextID)
  const res = await request.query<{ OrderID: number }>(`
    SELECT [${LearningObjectContextFields.OrderID}]
    FROM [${LearningObjectContextsTableName}]
    WHERE [${LearningObjectContextFields.LearningObjectID}] = @objectID
    AND [${LearningObjectContextFields.LearningContextID}] = @contextID
  `)
  if (res.recordset.length === 0) {
    return undefined
  } else {
    return res.recordset[0].OrderID
  }
}

async function getContextCount (request: Request, objectID: string): Promise<number> {
  request.input('id', objectID)
  const res = await request.query<{ contextCount: number }>(`
    SELECT COUNT([${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}]) AS contextCount
    FROM [${LearningObjectContextsTableName}]
    WHERE [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] = @id
  `)
  return res.recordset[0].contextCount
}
