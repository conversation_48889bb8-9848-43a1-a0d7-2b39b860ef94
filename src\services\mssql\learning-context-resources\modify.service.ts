import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningContextResourcesModel from '../../../models/learning-context-resources.model.js'
import { LearningContextResources } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'
import { type Request } from 'mssql'

export default async function modifyLearningContextResource (model: LearningContextResourcesModel, request?: Request): Promise<LearningContextResourcesModel> {
  request ??= mssql.getPool().request()
  const record = await updateRow<LearningContextResources>(request, model, { LearningContextID: model.fields.LearningContextID, LearningObjectID: model.fields.LearningObjectID })
  return new LearningContextResourcesModel(record[0])
}