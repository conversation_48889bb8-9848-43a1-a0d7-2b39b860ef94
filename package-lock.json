{"name": "tess-lms", "version": "2.1.3", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "tess-lms", "version": "2.1.3", "license": "Northrop Grumman", "dependencies": {"@json2csv/plainjs": "^7.0.6", "@lcs/logger": "^4.0.3", "@lcs/mssql-utility": "^3.0.0", "@lcs/rabbitmq": "^4.0.2", "@lcs/session-authority": "^4.0.0", "@tess-f/backend-utils": "^2.0.2", "@tess-f/email": "^3.0.2", "@tess-f/fds": "^2.0.0", "@tess-f/lms": "^2.0.7", "@tess-f/objectives": "^2.0.3", "@tess-f/shared-config": "^2.0.10", "@tess-f/sql-tables": "^2.2.3", "@tess-f/system-config": "^2.0.3", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "excel4node": "^1.8.2", "express": "^5.1.0", "http-status": "^2.1.0", "minimist": "^1.2.8", "moment-timezone": "^0.6.0", "mssql": "^11.0.1", "pdfkit": "^0.17.1", "prettyjson": "^1.2.5", "prom-client": "^15.1.3", "redis": "^5.5.6", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/chai": "^5.2.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/mssql": "^9.1.7", "@types/node": "^22.14.1", "@types/pdfkit": "^0.14.0", "@types/prettyjson": "^0.0.33", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "c8": "^10.1.3", "chai": "^5.2.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^9.29.0", "esmock": "^2.7.0", "globals": "^16.2.0", "mocha": "^11.7.0", "mocha-junit-reporter": "^2.2.1", "mocha-multi": "^1.1.7", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "sinon": "^21.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "uuid": "^11.1.0"}}, "node_modules/@aashutoshrathi/word-wrap": {"version": "1.2.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz", "integrity": "sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@acuminous/bitsyntax": {"version": "0.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@acuminous/bitsyntax/-/bitsyntax-0.1.2.tgz", "integrity": "sha512-29lUK80d1muEQqiUsSo+3A0yP6CdspgC95EnKBMi22Xlwt79i/En4Vr67+cXhU+cZjbti3TgGGC5wy1stIywVQ==", "license": "MIT", "dependencies": {"buffer-more-ints": "~1.0.0", "debug": "^4.3.4", "safe-buffer": "~5.1.2"}, "engines": {"node": ">=0.8"}}, "node_modules/@azure/abort-controller": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-1.1.0.tgz", "integrity": "sha512-TrRLIoSQVzfAJX9H1JeFjzAoDGcoK1IYX1UImfceTZpsyYfWr09Ss1aHW1y5TrrR3iq6RZLBwJ3E24uwPhwahw==", "license": "MIT", "dependencies": {"tslib": "^2.2.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@azure/core-auth": {"version": "1.7.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-auth/-/core-auth-1.7.2.tgz", "integrity": "sha512-Igm/S3fDYmnMq1uKS38Ae1/m37B3zigdlZw+kocwEhh5GjyKjPrXKO2J6rzpC1wAxrNil/jX9BJRqBshyjnF3g==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.1.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-auth/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-client": {"version": "1.9.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-client/-/core-client-1.9.2.tgz", "integrity": "sha512-kRdry/rav3fUKHl/aDLd/pDLcB+4pOFwPPTVEExuMyaI5r+JBbMWqRbCY1pn5BniDaU3lRxO9eaQ1AmSMehl/w==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-rest-pipeline": "^1.9.1", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.6.1", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-client/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http-compat": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-http-compat/-/core-http-compat-2.1.2.tgz", "integrity": "sha512-5MnV1yqzZwgNLLjlizsU3QqOeQChkIXw781Fwh1xdAqJR5AA32IUaq6xv1BICJvfbHoa+JYcaij2HFkhLbNTJQ==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-client": "^1.3.0", "@azure/core-rest-pipeline": "^1.3.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http-compat/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-lro": {"version": "2.7.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-lro/-/core-lro-2.7.2.tgz", "integrity": "sha512-0YIpccoX8m/k00O7mDDMdJpbr6mf1yWo2dfmxt5A8XVZVVMz2SSKaEbMCeJRvgQ0IaSlqhjT47p4hVIRRy90xw==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.2.0", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-lro/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-paging": {"version": "1.6.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-paging/-/core-paging-1.6.2.tgz", "integrity": "sha512-YKWi9YuCU04B55h25cnOYZHxXYtEvQEbKST5vqRga7hWY9ydd3FZHdeQF8pyh+acWZvppw13M/LMGx0LABUVMA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-rest-pipeline": {"version": "1.16.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-rest-pipeline/-/core-rest-pipeline-1.16.0.tgz", "integrity": "sha512-CeuTvsXxCUmEuxH5g/aceuSl6w2EugvNHKAtKKVdiX915EjJJxAwfzNNWZreNnbxHZ2fi0zaM6wwS23x2JVqSQ==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.9.0", "@azure/logger": "^1.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-rest-pipeline/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-tracing": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-tracing/-/core-tracing-1.1.2.tgz", "integrity": "sha512-dawW9ifvWAWmUm9/h+/UQ2jrdvjCJ7VJEuCJ6XVNudzcOwm53BFZH4Q845vjfgoUAM8ZxokvVNxNxAITc502YA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-util": {"version": "1.9.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/core-util/-/core-util-1.9.0.tgz", "integrity": "sha512-<PERSON><PERSON>lUQ1ZppaKuxPPMsFEUdX6GZPB3d9paR9d/TTL7Ow2De8cJaC7ibi7kWVlFAVPCYo31OcnGymc0R89DX8Oaw==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-util/node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/identity": {"version": "4.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/identity/-/identity-4.2.1.tgz", "integrity": "sha512-U8hsyC9YPcEIzoaObJlRDvp7KiF0MGS7xcWbyJSVvXRkC/HXo1f0oYeBYmEvVgRfacw7GHf6D6yAoh9JHz6A5Q==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-auth": "^1.5.0", "@azure/core-client": "^1.4.0", "@azure/core-rest-pipeline": "^1.1.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.3.0", "@azure/logger": "^1.0.0", "@azure/msal-browser": "^3.11.1", "@azure/msal-node": "^2.9.2", "events": "^3.0.0", "jws": "^4.0.0", "open": "^8.0.0", "stoppable": "^1.1.0", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/keyvault-keys": {"version": "4.8.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/keyvault-keys/-/keyvault-keys-4.8.0.tgz", "integrity": "sha512-jkuYxgkw0aaRfk40OQhFqDIupqblIOIlYESWB6DKCVDxQet1pyv86Tfk9M+5uFM0+mCs6+MUHU+Hxh3joiUn4Q==", "license": "MIT", "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-auth": "^1.3.0", "@azure/core-client": "^1.5.0", "@azure/core-http-compat": "^2.0.1", "@azure/core-lro": "^2.2.0", "@azure/core-paging": "^1.1.1", "@azure/core-rest-pipeline": "^1.8.1", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.0.0", "@azure/logger": "^1.0.0", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/logger": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/logger/-/logger-1.1.2.tgz", "integrity": "sha512-l170uE7bsKpIU6B/giRc9i4NI0Mj+tANMMMxf7Zi/5cKzEqPayP7+X1WPrG7e+91JgY8N+7K7nF2WOi7iVhXvg==", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/msal-browser": {"version": "3.17.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/msal-browser/-/msal-browser-3.17.0.tgz", "integrity": "sha512-csccKXmW2z7EkZ0I3yAoW/offQt+JECdTIV/KrnRoZyM7wCSsQWODpwod8ZhYy7iOyamcHApR9uCh0oD1M+0/A==", "license": "MIT", "dependencies": {"@azure/msal-common": "14.12.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "14.12.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/msal-common/-/msal-common-14.12.0.tgz", "integrity": "sha512-IDDXmzfdwmDkv4SSmMEyAniJf6fDu3FJ7ncOjlxkDuT85uSnLEhZi3fGZpoR7T4XZpOMx9teM9GXBgrfJgyeBw==", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-node": {"version": "2.9.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@azure/msal-node/-/msal-node-2.9.2.tgz", "integrity": "sha512-8tvi6Cos3m+0KmRbPjgkySXi+UQU/QiuVRFnrxIwt5xZlEEFa69O04RTaNESGgImyBBlYbo2mfE8/U8Bbdk1WQ==", "license": "MIT", "dependencies": {"@azure/msal-common": "14.12.0", "jsonwebtoken": "^9.0.0", "uuid": "^8.3.0"}, "engines": {"node": ">=16"}}, "node_modules/@azure/msal-node/node_modules/uuid": {"version": "8.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@bcoe/v8-coverage": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-1.0.2.tgz", "integrity": "sha512-6zABk/ECA/QYSCQ1NGiVwwbQerUCZ+TQbp64Q3AgmfNvurHH0j8TtXa1qbShXA6qqkpAj4V5W8pP6mLe1mcMqA==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@colors/colors": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@colors/colors/-/colors-1.6.0.tgz", "integrity": "sha512-Ir+AOibqzrIsL6ajt3Rz3LskB7OiMVHqltZmspbW/TJuTVuyOMirVqAkjfY6JISiLHgyNqicAC8AyHHGzNd/dA==", "engines": {"node": ">=0.1.90"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "integrity": "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@dabh/diagnostics": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@dabh/diagnostics/-/diagnostics-2.0.3.tgz", "integrity": "sha512-hrlQOIi7hAfzsMqlGSFyVucrx38O+j6wiGOf//H2ecvIEqYN4ADBSS2iLMh5UFyDunCNniUIPk/q3riFv45xRA==", "dependencies": {"colorspace": "1.1.x", "enabled": "2.0.x", "kuler": "^2.0.0"}}, "node_modules/@elastic/ecs-helpers": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/@elastic/ecs-helpers/-/ecs-helpers-2.1.1.tgz", "integrity": "sha512-ItoNazMnYdlUCmkBYTXc3SG6PF7UlVTbvMdHPvXkfTMPdwGv2G1Xtp5CjDHaGHGOZSwaDrW4RSCXvA/lMSU+rg==", "optional": true, "engines": {"node": ">=10"}}, "node_modules/@elastic/ecs-pino-format": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@elastic/ecs-pino-format/-/ecs-pino-format-1.5.0.tgz", "integrity": "sha512-7MMVmT50ucEl7no8mUgCIl+pffBVNRl36uZi0vmalWa2xPWISBxM9k9WSP/WTgOkmGj9G35e5g3UfCS1zxshBg==", "optional": true, "dependencies": {"@elastic/ecs-helpers": "^2.1.1"}, "engines": {"node": ">=10"}}, "node_modules/@elastic/elasticsearch": {"version": "7.17.14", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@elastic/elasticsearch/-/elasticsearch-7.17.14.tgz", "integrity": "sha512-6uQ1pVXutwz1Krwooo67W+3K8BwH1ASMh1WoHTpomUzw8EXecXN5lHIJ9EPqTHuv1WqR2LKkSJyagcq0HYUJpg==", "license": "Apache-2.0", "dependencies": {"debug": "^4.3.1", "hpagent": "^0.1.1", "ms": "^2.1.3", "secure-json-parse": "^2.4.0"}, "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/config-array/-/config-array-0.20.1.tgz", "integrity": "sha512-OL0RJzC/CBzli0DrrR31qzj6d6i6Mm3HByuhflhl4LOBiWxN+3i6/t/ZQQNii4tjksXi8r2CRW1wMpWA2ULUEw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/config-helpers/-/config-helpers-0.2.1.tgz", "integrity": "sha512-RI17tsD2frtDu/3dmI7QRrD4bedNKPM08ziRYaC5AhkGrzIAJelm9kJU1TznK+apx6V+cqRz8tfpEeG3oIyjxw==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.14.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/core/-/core-0.14.0.tgz", "integrity": "sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/globals/-/globals-14.0.0.tgz", "integrity": "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "9.29.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/js/-/js-9.29.0.tgz", "integrity": "sha512-3PIF4cBw/y+1u2EazflInpV+lYsSG0aByVIQzAgb1m1MhHFSbqTyNqtBKHgWf/9Ykud+DhILS9EGkmekVhbKoQ==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/plugin-kit/-/plugin-kit-0.3.2.tgz", "integrity": "sha512-4SaFZCNfJqvk/kenHpI8xvN42DMaoycy4PzKc5otHxRswww1kAt82OlBuwRVLofCACCTZEcla2Ydxv8scMXaTg==", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.15.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit/node_modules/@eslint/core": {"version": "0.15.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@eslint/core/-/core-0.15.0.tgz", "integrity": "sha512-b7ePw78tEWWkpgZCDYkbqDOP8dmM6qe+AOC6iuJqlq1R/0ahMAeH3qynpnqKFGkMltrp44ohV4ubGyvLX28tzw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@humanwhocodes/retry/-/retry-0.4.2.tgz", "integrity": "sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@isaacs/cliui/-/cliui-8.0.2.tgz", "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ansi-regex/-/ansi-regex-6.0.1.tgz", "integrity": "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string-width/-/string-width-5.1.2.tgz", "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@js-joda/core": {"version": "5.6.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@js-joda/core/-/core-5.6.2.tgz", "integrity": "sha512-ow4R+7C24xeTjiMTTZ4k6lvxj7MRBqvqLCQjThQff3RjOmIMokMP20LNYVFhGafJtUx/Xo2Qp4qU8eNoTVH0SA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@json2csv/formatters": {"version": "7.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@json2csv/formatters/-/formatters-7.0.6.tgz", "integrity": "sha512-hjIk1H1TR4ydU5ntIENEPgoMGW+Q7mJ+537sDFDbsk+Y3EPl2i4NfFVjw0NJRgT+ihm8X30M67mA8AS6jPidSA==", "license": "MIT"}, "node_modules/@json2csv/plainjs": {"version": "7.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@json2csv/plainjs/-/plainjs-7.0.6.tgz", "integrity": "sha512-4Md7RPDCSYpmW1HWIpWBOqCd4vWfIqm53S3e/uzQ62iGi7L3r34fK/8nhOMEe+/eVfCx8+gdSCt1d74SlacQHw==", "license": "MIT", "dependencies": {"@json2csv/formatters": "^7.0.6", "@streamparser/json": "^0.0.20"}}, "node_modules/@lcs/jwt": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@lcs/jwt/-/jwt-2.0.0.tgz", "integrity": "sha512-SpFdGHFyfsvyXpnqexgLV38l2l8orFdcYLGcnY3Nmd6nUSiBYkOfNHwZGwTGNqKzbbE2xz3M3r0J0F1Nqr3AaQ==", "license": "ISC"}, "node_modules/@lcs/logger": {"version": "4.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@lcs/logger/-/logger-4.0.3.tgz", "integrity": "sha512-MAPSEzf9e4YE5AcevjgNaCRcllOrACdn46HenU8rumU8WwmHPcVH4ttm+6FW4IisZm6I1KqLaoNtEyVkvASlsA==", "license": "ISC", "dependencies": {"@elastic/elasticsearch": "^7.17.14", "chalk": "^5.3.0", "strip-color": "^0.1.0", "uninstall": "^0.0.0", "winston": "^3.17.0", "winston-elasticsearch": "^0.16.1"}}, "node_modules/@lcs/logger/node_modules/chalk": {"version": "5.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/chalk/-/chalk-5.3.0.tgz", "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@lcs/mssql-utility": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@lcs/mssql-utility/-/mssql-utility-3.0.0.tgz", "integrity": "sha512-HRvXJbhGs9Z9LDWYz0DN3tAv5LZXPH+fpUymDJ6vw4K2dxHUc+qAihvUFq2/0ZZw8OuECo/g27gZ/JwbX0IOWg==", "license": "ISC", "dependencies": {"@types/node": "^20.14.8", "mssql": "^11.0.0"}}, "node_modules/@lcs/mssql-utility/node_modules/@types/node": {"version": "20.14.13", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/node/-/node-20.14.13.tgz", "integrity": "sha512-+bHoGiZb8UiQ0+WEtmph2IWQCjIqg8MDZMAV+ppRRhUZnquF5mQkP/9vpSwJClEiSM/C7fZZExPzfU0vJTyp8w==", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@lcs/mssql-utility/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "license": "MIT"}, "node_modules/@lcs/rabbitmq": {"version": "4.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@lcs/rabbitmq/-/rabbitmq-4.0.2.tgz", "integrity": "sha512-duwLL7URKtRbHGd67QNcswcF1/I+HvK/cU/ebt8wF4RhRtprJYR4cdpyYPADuKy32a8/9JzX0hQ1AYcTeH6m8A==", "license": "ISC", "dependencies": {"amqplib": "^0.10.4", "uuid": "^10.0.0"}}, "node_modules/@lcs/rabbitmq/node_modules/uuid": {"version": "10.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@lcs/session-authority": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@lcs/session-authority/-/session-authority-4.0.0.tgz", "integrity": "sha512-eB6aFbSTaKSUCOPoIhhsdxbiNu7hX1IRdWVuWeVPYWn0tySFAg5NpAhuc81yW3np6Z8W9snNexUtPvsL2juGZA==", "license": "MIT", "dependencies": {"@lcs/jwt": "^2.0.0", "emittery": "^1.0.3", "redis": "^4.7.0", "socket.io": "^4.8.1", "uuid": "^11.0.3"}, "peerDependencies": {"express": "5.x"}}, "node_modules/@lcs/session-authority/node_modules/@redis/bloom": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/bloom/-/bloom-1.2.0.tgz", "integrity": "sha512-HG2DFjYKbpNmVXsa0keLHp/3leGJz1mjh09f2RLGGLQZzSHpkmZWuwJbAvo3QcRY8p80m5+ZdXZdYOSBLlp7Cg==", "license": "MIT", "peerDependencies": {"@redis/client": "^1.0.0"}}, "node_modules/@lcs/session-authority/node_modules/@redis/json": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/json/-/json-1.0.7.tgz", "integrity": "sha512-6UyXfjVaTBTJtKNG4/9Z8PSpKE6XgSyEb8iwaqDcy+uKrd/DGYHTWkUdnQDyzm727V7p21WUMhsqz5oy65kPcQ==", "license": "MIT", "peerDependencies": {"@redis/client": "^1.0.0"}}, "node_modules/@lcs/session-authority/node_modules/@redis/search": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/search/-/search-1.2.0.tgz", "integrity": "sha512-tYoDBbtqOVigEDMAcTGsRlMycIIjwMCgD8eR2t0NANeQmgK/lvxNAvYyb6bZDD4frHRhIHkJu2TBRvB0ERkOmw==", "license": "MIT", "peerDependencies": {"@redis/client": "^1.0.0"}}, "node_modules/@lcs/session-authority/node_modules/redis": {"version": "4.7.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/redis/-/redis-4.7.1.tgz", "integrity": "sha512-S1bJDnqLftzHXHP8JsT5II/CtHWQrASX5K96REjWjlmWKrviSOLWmM7QnRLstAWsu1VBBV1ffV6DzCvxNP0UJQ==", "license": "MIT", "workspaces": ["./packages/*"], "dependencies": {"@redis/bloom": "1.2.0", "@redis/client": "1.6.1", "@redis/graph": "1.1.1", "@redis/json": "1.0.7", "@redis/search": "1.2.0", "@redis/time-series": "1.1.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@opentelemetry/api": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/@opentelemetry/api/-/api-1.7.0.tgz", "integrity": "sha512-AdY5wvN0P2vXBi3b29hxZgSFvdhdxPB9+f0B6s//P9Q8nibRWeA3cHm8UmLpio9ABigkVHJ5NMPk+Mz8VCCyrw==", "engines": {"node": ">=8.0.0"}}, "node_modules/@opentelemetry/core": {"version": "1.21.0", "resolved": "https://registry.npmjs.org/@opentelemetry/core/-/core-1.21.0.tgz", "integrity": "sha512-K<PERSON>+OIweb3wYoP7qTYL/j5IpOlu52uxBv5M4+QhSmmUfLyTgu1OIS71msK3chFo1D6Y61BIH3wMiMYRCxJCQctA==", "optional": true, "dependencies": {"@opentelemetry/semantic-conventions": "1.21.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.8.0"}}, "node_modules/@opentelemetry/resources": {"version": "1.21.0", "resolved": "https://registry.npmjs.org/@opentelemetry/resources/-/resources-1.21.0.tgz", "integrity": "sha512-1Z86FUxPKL6zWVy2LdhueEGl9AHDJcx+bvHStxomruz6Whd02mE3lNUMjVJ+FGRoktx/xYQcxccYb03DiUP6Yw==", "optional": true, "dependencies": {"@opentelemetry/core": "1.21.0", "@opentelemetry/semantic-conventions": "1.21.0"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.8.0"}}, "node_modules/@opentelemetry/sdk-metrics": {"version": "1.21.0", "resolved": "https://registry.npmjs.org/@opentelemetry/sdk-metrics/-/sdk-metrics-1.21.0.tgz", "integrity": "sha512-on1jTzIHc5DyWhRP+xpf+zrgrREXcHBH4EDAfaB5mIG7TWpKxNXooQ1JCylaPsswZUv4wGnVTinr4HrBdGARAQ==", "optional": true, "dependencies": {"@opentelemetry/core": "1.21.0", "@opentelemetry/resources": "1.21.0", "lodash.merge": "^4.6.2"}, "engines": {"node": ">=14"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.8.0"}}, "node_modules/@opentelemetry/semantic-conventions": {"version": "1.21.0", "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.21.0.tgz", "integrity": "sha512-lkC8kZYntxVKr7b8xmjCVUgE0a8xgDakPyDo9uSWavXPyYqLgYYGdEd2j8NxihRyb6UwpX3G/hFUF4/9q2V+/g==", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@redis/bloom": {"version": "5.5.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/bloom/-/bloom-5.5.6.tgz", "integrity": "sha512-bNR3mxkwtfuCxNOzfV8B3R5zA1LiN57EH6zK4jVBIgzMzliNuReZXBFGnXvsi80/SYohajn78YdpYI+XNpqL+A==", "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"@redis/client": "^5.5.6"}}, "node_modules/@redis/client": {"version": "1.5.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/client/-/client-1.5.8.tgz", "integrity": "sha512-xzElwHIO6rBAqzPeVnCzgvrnBEcFL1P0w8P65VNLRkdVW8rOE58f52hdj0BDgmsdOm4f1EoXPZtH4Fh7M/qUpw==", "license": "MIT", "dependencies": {"cluster-key-slot": "1.1.2", "generic-pool": "3.9.0", "yallist": "4.0.0"}, "engines": {"node": ">=14"}}, "node_modules/@redis/client/node_modules/yallist": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yallist/-/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/@redis/graph": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/graph/-/graph-1.1.1.tgz", "integrity": "sha512-FEMTcTHZozZciLRl6GiiIB4zGm5z5F3F6a6FZCyrfxdKOhFlGkiAqlexWMBzCi4DcRoyiOsuLfW+cjlGWyExOw==", "license": "MIT", "peerDependencies": {"@redis/client": "^1.0.0"}}, "node_modules/@redis/json": {"version": "5.5.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/json/-/json-5.5.6.tgz", "integrity": "sha512-AIsoe3SsGQagqAmSQHaqxEinm5oCWr7zxPWL90kKaEdLJ+zw8KBznf2i9oK0WUFP5pFssSQUXqnscQKe2amfDQ==", "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"@redis/client": "^5.5.6"}}, "node_modules/@redis/search": {"version": "5.5.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/search/-/search-5.5.6.tgz", "integrity": "sha512-JSqasYqO0mVcHL7oxvbySRBBZYRYhFl3W7f0Da7BW8M/r0Z9wCiVrdjnN4/mKBpWZkoJT/iuisLUdPGhpKxBew==", "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"@redis/client": "^5.5.6"}}, "node_modules/@redis/time-series": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@redis/time-series/-/time-series-1.0.4.tgz", "integrity": "sha512-ThUIgo2U/g7cCuZavucQTQzA9g9JbDDY2f64u3AbAoz/8vE2lt2U37LamDUVChhaDA3IRT9R6VvJwqnUfTJzng==", "license": "MIT", "peerDependencies": {"@redis/client": "^1.0.0"}}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@sinonjs/commons/-/commons-3.0.1.tgz", "integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "13.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz", "integrity": "sha512-36/hTbH2uaWuGVERyC6da9YwGWnzUZXuPro/F2LfsdOsLnCojz/iSH8MxUt/FD2S5XBSVPhmArFUXcpCQ2Hkiw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.1"}}, "node_modules/@sinonjs/samsam": {"version": "8.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@sinonjs/samsam/-/samsam-8.0.2.tgz", "integrity": "sha512-v46t/fwnhejRSFTGqbpn9u+LQ9xJDse10gNnPgAcxgdoCDMXj/G2asWAC/8Qs+BAZDicX+MNZouXT1A7c83kVw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.1", "lodash.get": "^4.4.2", "type-detect": "^4.1.0"}}, "node_modules/@sinonjs/samsam/node_modules/type-detect": {"version": "4.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-detect/-/type-detect-4.1.0.tgz", "integrity": "sha512-Acylog8/luQ8L7il+geoSxhEkazvkslg7PSNKOX59mbB9cOveP5aq9h74Y7YU8yDpJwetzQQrfIwtf4Wp4LKcw==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@socket.io/component-emitter": {"version": "3.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz", "integrity": "sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==", "license": "MIT"}, "node_modules/@streamparser/json": {"version": "0.0.20", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@streamparser/json/-/json-0.0.20.tgz", "integrity": "sha512-VqAAkydywPpkw63WQhPVKCD3SdwXuihCUVZbbiY3SfSTGQyHmwRoq27y4dmJdZuJwd5JIlQoMPyGvMbUPY0RKQ==", "license": "MIT"}, "node_modules/@swc/helpers": {"version": "0.5.17", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@swc/helpers/-/helpers-0.5.17.tgz", "integrity": "sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@tediousjs/connection-string": {"version": "0.5.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tediousjs/connection-string/-/connection-string-0.5.0.tgz", "integrity": "sha512-7qSgZbincDDDFyRweCIEvZULFAw5iz/DeunhvuxpL31nfntX3P4Yd4HkHBRg9H8CdqY1e5WFN1PZIz/REL9MVQ==", "license": "MIT"}, "node_modules/@tess-f/backend-utils": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/backend-utils/-/backend-utils-2.0.2.tgz", "integrity": "sha512-FD9obQCVE4s8TI9taf0o19XjZqOkjVrnzOlX2VsgzM3FDN/yKX4GMWLfxCMMKw2P0GzA5nsavwXRjiuYHJDAjg==", "license": "ISC", "dependencies": {"@lcs/logger": "^4.0.3", "response-time": "^2.3.3"}, "optionalDependencies": {"@esbuild/linux-64": "0.24.2"}, "peerDependencies": {"express": "5.x", "prom-client": ">= 10.x <=15.x", "zod": ">= 3.x"}}, "node_modules/@tess-f/email": {"version": "3.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/email/-/email-3.0.2.tgz", "integrity": "sha512-4LtHg4J80yEGI3ndwQVqujsVV8gbHkoi8deaGPsBgwHhXL8Ol/+/2g8pyRVnMlxTe9Jb0YecyaQK8zRhW4ZmjA==", "license": "ISC", "dependencies": {"@lcs/rabbitmq": "^4.0.2", "@tess-f/shared-config": "^2.0.1", "@tess-f/sql-tables": "^2.0.3"}}, "node_modules/@tess-f/fds": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/fds/-/fds-2.0.0.tgz", "integrity": "sha512-Eez3YFMl13rpJF3KlxXxhYm7v2VcEm1WfufvtR272HhytTjNXXf17VI8u9Muu6n9zFq24iizQs4JrYJ+VlJsOA==", "license": "ISC", "dependencies": {"@lcs/rabbitmq": "^4.0.2", "@tess-f/shared-config": "^2.0.1"}}, "node_modules/@tess-f/lms": {"version": "2.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/lms/-/lms-2.0.7.tgz", "integrity": "sha512-NaKiUq4eaydIHpz9Vg4Kk/VQe0sNk/mocWEPinssAdfo+ZXA38cZO4ONWu/pPYaVdiTxmxB0HcrzUpHjQVC6sg==", "license": "ISC", "dependencies": {"@lcs/rabbitmq": "^4.0.2", "@tess-f/shared-config": "^2.0.5"}, "peerDependencies": {"@tess-f/sql-tables": "2.x"}}, "node_modules/@tess-f/objectives": {"version": "2.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/objectives/-/objectives-2.0.3.tgz", "integrity": "sha512-QIDbstN08tfIXVS9ry5lFKc8SMEHu6TLgrfjDmWjUHoitrFRzbVwv7ROEtHK2L8oDwj16OyV8rTw5oFipo1KcA==", "license": "ISC", "dependencies": {"@lcs/rabbitmq": "^4.0.2", "@tess-f/shared-config": "^2.0.3", "@tess-f/sql-tables": "^2.1.15"}}, "node_modules/@tess-f/shared-config": {"version": "2.0.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/shared-config/-/shared-config-2.0.10.tgz", "integrity": "sha512-C0Gy3Mb06CvfNZ2IlZbvF2ZZEL6ZwyFBZHmWvnk9Sm7URQB4zrEPLBJBHT4JEpoSXKgXKntYQU6GDU0lJbA2/g==", "license": "ISC", "dependencies": {"deepmerge": "^4.3.1", "js-yaml": "^4.1.0"}}, "node_modules/@tess-f/sql-tables": {"version": "2.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/sql-tables/-/sql-tables-2.2.3.tgz", "integrity": "sha512-g7ZDbQI90jZI3m/VYWKKKBNn1yWYPFku9d5tXcvDWEMTkUYTzPGkKbqmRItncN8joxp3Eq5R841Uj+TxkwBgHQ==", "license": "ISC"}, "node_modules/@tess-f/system-config": {"version": "2.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tess-f/system-config/-/system-config-2.0.3.tgz", "integrity": "sha512-qDBQ/pHOdok77ofoIa76OD3Yowcg9nyY13Qv2v2vOfcOYG78+ZB8e4zE+JBGtSsXtJjJWc541f2U5eYl6YvnRg==", "license": "ISC", "dependencies": {"@lcs/rabbitmq": "^4.0.2", "@tess-f/shared-config": "^2.0.1"}, "peerDependencies": {"@tess-f/sql-tables": "^2.1.34"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==", "dev": true, "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/body-parser/-/body-parser-1.19.2.tgz", "integrity": "sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/chai": {"version": "5.2.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/chai/-/chai-5.2.2.tgz", "integrity": "sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/connect": {"version": "3.4.35", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/connect/-/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cookie-parser": {"version": "1.4.9", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/cookie-parser/-/cookie-parser-1.4.9.tgz", "integrity": "sha512-tGZiZ2Gtc4m3wIdLkZ8mkj1T6CEHb35+VApbL2T14Dew8HA7c+04dmKqsKRNC+8RJPm16JEK0tFSwdZqubfc4g==", "dev": true, "license": "MIT", "peerDependencies": {"@types/express": "*"}}, "node_modules/@types/cors": {"version": "2.8.17", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/cors/-/cors-2.8.17.tgz", "integrity": "sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/deep-eql/-/deep-eql-4.0.2.tgz", "integrity": "sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "5.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/express/-/express-5.0.3.tgz", "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/express-serve-static-core/-/express-serve-static-core-5.0.0.tgz", "integrity": "sha512-AbXMTZGt40T+KON9/Fdxx0B2WK5hsgxcfXJLr5bFpZ7b4JCex2WyQPTEKdXqfHiY5nKKBScZ7yCoO6Pvgxfvnw==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/mime/-/mime-3.0.1.tgz", "integrity": "sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=", "dev": true, "license": "MIT"}, "node_modules/@types/minimist": {"version": "1.2.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/minimist/-/minimist-1.2.5.tgz", "integrity": "sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==", "dev": true, "license": "MIT"}, "node_modules/@types/mocha": {"version": "10.0.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/mocha/-/mocha-10.0.10.tgz", "integrity": "sha512-xPyYSz1cMPnJQhl0CLMH68j3gprKZaTjG3s5Vi+fDgx+uhG9NOXwbVt52eFS8ECyXhyKcjDLCBEqBExKuiZb7Q==", "dev": true, "license": "MIT"}, "node_modules/@types/mssql": {"version": "9.1.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/mssql/-/mssql-9.1.7.tgz", "integrity": "sha512-eIOEe78nuSW5KctDHImDhLZ9a+jV/z/Xs5RBhcG/jrk+YWqhdNmzBmHVWV7aWQ5fW+jbIGtX6Ph+bbVqfhzafg==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "tarn": "^3.0.1", "tedious": "*"}}, "node_modules/@types/node": {"version": "22.14.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/node/-/node-22.14.1.tgz", "integrity": "sha512-u0HuPQwe/dHrItgHHpmw3N2fYCR6x4ivMNbPHRkBVP4CvN+kiRrKHWk3i8tXiO/joPwXLMYvF9TTF0eqgHIuOw==", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/pdfkit": {"version": "0.14.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/pdfkit/-/pdfkit-0.14.0.tgz", "integrity": "sha512-X94hoZVr9dNfV23roeXRm57AWS+AOMak3gq2wZvn4TXiLvXE8+TrYaM5IkMyZbGRw49jEqI49rP/UVL3+C3Svg==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/prettyjson": {"version": "0.0.33", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/prettyjson/-/prettyjson-0.0.33.tgz", "integrity": "sha512-hHZMkavT9OXFq8p6pTCiaREtPxMRfy9NMp+Qa4PWH0RINQjyh0crOhoqUFA/cvIZncpjBpdvxkoe7nmVbyBJXw==", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.16", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/qs/-/qs-6.9.16.tgz", "integrity": "sha512-7i+zxXdPD0T4cKDuxCUXJ4wHcsJLwENa6Z3dCu8cfCK743OGy5Nu1RmAGqDPsoTDINVEcdXKRvR/zre+P2Ku1A==", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==", "dev": true, "license": "MIT"}, "node_modules/@types/readable-stream": {"version": "4.0.14", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/readable-stream/-/readable-stream-4.0.14.tgz", "integrity": "sha512-xZn/AuUbCMShGsqH/ehZtGDwQtbx00M9rZ2ENLe4tOjFZ/JFeWMhEZkk2fEe1jAUqqEAURIkFJ7Az/go8mM1/w==", "license": "MIT", "dependencies": {"@types/node": "*", "safe-buffer": "~5.1.1"}}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/send/-/send-0.17.4.tgz", "integrity": "sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/send/node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==", "dev": true, "license": "MIT"}, "node_modules/@types/serve-static": {"version": "1.15.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/serve-static/-/serve-static-1.15.1.tgz", "integrity": "sha512-NUo5XNiAdULrJENtJXZZ3fHtfMolzZwczzBbnAeBbqBwG+LaG6YaJtuwzwGSQZ2wsCrxjEhNNjAkKigy3n8teQ==", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "*", "@types/node": "*"}}, "node_modules/@types/sinon": {"version": "17.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/sinon/-/sinon-17.0.4.tgz", "integrity": "sha512-RHnIrhfPO3+tJT0s7cFaXGZvsL4bbR3/k7z3P312qMS4JaS2Tk+KiwiLx1S0rQ56ERj00u1/BtdyVd0FY+Pdew==", "dev": true, "license": "MIT", "dependencies": {"@types/sinonjs__fake-timers": "*"}}, "node_modules/@types/sinonjs__fake-timers": {"version": "8.1.5", "resolved": "https://registry.npmjs.org/@types/sinonjs__fake-timers/-/sinonjs__fake-timers-8.1.5.tgz", "integrity": "sha512-mQkU2jY8jJEF7YHjHvsQO8+3ughTL1mcnn96igfhONmR+fUPSKIkefQYpSe8bsly2Ep7oQbn/6VG5/9/0qcArQ==", "dev": true}, "node_modules/@types/triple-beam": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/@types/triple-beam/-/triple-beam-1.3.5.tgz", "integrity": "sha512-6WaYesThRMCl19iryMYP7/x2OVgCtbIVflDGFpWnb9irXI3UjYE4AzmYuiUKY1AJstGijoY+MgUszMgRxIYTYw=="}, "node_modules/@types/uuid": {"version": "10.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@types/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.34.1.tgz", "integrity": "sha512-STXcN6ebF6li4PxwNeFnqF8/2BNDvBupf2OPx2yWNzr6mKNGF7q49VM00Pz5FaomJyqvbXpY6PhO+T9w139YEQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.34.1", "@typescript-eslint/type-utils": "8.34.1", "@typescript-eslint/utils": "8.34.1", "@typescript-eslint/visitor-keys": "8.34.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.34.1", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ignore/-/ignore-7.0.5.tgz", "integrity": "sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/parser/-/parser-8.34.1.tgz", "integrity": "sha512-4O3idHxhyzjClSMJ0a29AcoK0+YwnEqzI6oz3vlRf3xw0zbzt15MzXwItOlnr5nIth6zlY2RENLsOPvhyrKAQA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.34.1", "@typescript-eslint/types": "8.34.1", "@typescript-eslint/typescript-estree": "8.34.1", "@typescript-eslint/visitor-keys": "8.34.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/project-service/-/project-service-8.34.1.tgz", "integrity": "sha512-nuHlOmFZfuRwLJKDGQOVc0xnQrAmuq1Mj/ISou5044y1ajGNp2BNliIqp7F2LPQ5sForz8lempMFCovfeS1XoA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.34.1", "@typescript-eslint/types": "^8.34.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/scope-manager/-/scope-manager-8.34.1.tgz", "integrity": "sha512-beu6o6QY4hJAgL1E8RaXNC071G4Kso2MGmJskCFQhRhg8VOH/FDbC8soP8NHN7e/Hdphwp8G8cE6OBzC8o41ZA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.34.1", "@typescript-eslint/visitor-keys": "8.34.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1.tgz", "integrity": "sha512-K4Sjdo4/xF9NEeA2khOb7Y5nY6NSXBnod87uniVYW9kHP+hNlDV8trUSFeynA2uxWam4gIWgWoygPrv9VMWrYg==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/type-utils/-/type-utils-8.34.1.tgz", "integrity": "sha512-Tv7tCCr6e5m8hP4+xFugcrwTOucB8lshffJ6zf1mF1TbU67R+ntCc6DzLNKM+s/uzDyv8gLq7tufaAhIBYeV8g==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "8.34.1", "@typescript-eslint/utils": "8.34.1", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/types/-/types-8.34.1.tgz", "integrity": "sha512-rjLVbmE7HR18kDsjNIZQHxmv9RZwlgzavryL5Lnj2ujIRTeXlKtILHgRNmQ3j4daw7zd+mQgy+uyt6Zo6I0IGA==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/typescript-estree/-/typescript-estree-8.34.1.tgz", "integrity": "sha512-rjCNqqYPuMUF5ODD+hWBNmOitjBWghkGKJg6hiCHzUvXRy6rK22Jd3rwbP2Xi+R7oYVvIKhokHVhH41BxPV5mA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.34.1", "@typescript-eslint/tsconfig-utils": "8.34.1", "@typescript-eslint/types": "8.34.1", "@typescript-eslint/visitor-keys": "8.34.1", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@typescript-eslint/utils": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/utils/-/utils-8.34.1.tgz", "integrity": "sha512-mqOwUdZ3KjtGk7xJJnLbHxTuWVn3GO2WZZuM+Slhkun4+qthLdXx32C8xIXbO1kfCECb3jIs3eoxK3eryk7aoQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.34.1", "@typescript-eslint/types": "8.34.1", "@typescript-eslint/typescript-estree": "8.34.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/@typescript-eslint/visitor-keys/-/visitor-keys-8.34.1.tgz", "integrity": "sha512-xoh5rJ+tgsRKoXnkBPFRLZ7rjKM0AfVbC68UZ/ECXoDbfggb9RbEySN359acY1vS3qZ0jVTVWzbtfapwm5ztxw==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.34.1", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/visitor-keys/node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "dev": true, "license": "ISC"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/acorn/-/acorn-8.15.0.tgz", "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "devOptional": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-assertions": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz", "integrity": "sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==", "optional": true, "peerDependencies": {"acorn": "^8"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/acorn-walk/-/acorn-walk-8.3.3.tgz", "integrity": "sha512-MxXdReSRhGO7VlFe1bRG/oI7/mdLV9B9JJT0N8vZOhF7gFRR5l3M8W9G8JxmKV+JC5mGqJ0QvqfSOLsCPa4nUw==", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/after-all-results": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/after-all-results/-/after-all-results-2.0.0.tgz", "integrity": "sha512-2zHEyuhSJOuCrmas9YV0YL/MFCWLxe1dS6k/ENhgYrb/JqyMnadLN4iIAc9kkZrbElMDyyAGH/0J18OPErOWLg==", "optional": true}, "node_modules/agent-base": {"version": "7.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/agent-base/-/agent-base-7.1.1.tgz", "integrity": "sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==", "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/agentkeepalive": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz", "integrity": "sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==", "optional": true, "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "node_modules/amqplib": {"version": "0.10.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/amqplib/-/amqplib-0.10.4.tgz", "integrity": "sha512-DMZ4eCEjAVdX1II2TfIUpJhfKAuoCeDIo/YyETbfAqehHTXxxs7WOOd+N1Xxr4cKhx12y23zk8/os98FxlZHrw==", "license": "MIT", "dependencies": {"@acuminous/bitsyntax": "^0.1.2", "buffer-more-ints": "~1.0.0", "readable-stream": "1.x >=1.1.9", "url-parse": "~1.5.10"}, "engines": {"node": ">=10"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/arg/-/arg-4.1.3.tgz", "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "license": "Python-2.0"}, "node_modules/array-buffer-byte-length": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz", "integrity": "sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.2.tgz", "integrity": "sha512-yMBKppFur/fbHu9/6USUe03bZ4knMYiwFBcyiaXB8Go0qNehwX6inYPzK9U0NeQvGxKthcmHcaR8P5MStSRBAw==", "license": "MIT", "optional": true, "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "get-intrinsic": "^1.2.1", "is-array-buffer": "^3.0.2", "is-shared-array-buffer": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "integrity": "sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA=="}, "node_modules/assertion-error": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/assertion-error/-/assertion-error-2.0.1.tgz", "integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "license": "MIT"}, "node_modules/async-cache": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/async-cache/-/async-cache-1.1.0.tgz", "integrity": "sha512-YDQc4vBn5NFhY6g6HhVshyi3Fy9+SQ5ePnE7JLDJn1DoL+i7ER+vMwtTNOYk9leZkYMnOwpBCWqyLDPw8Aig8g==", "deprecated": "No longer maintained. Use [lru-cache](http://npm.im/lru-cache) version 7.6 or higher, and provide an asynchronous `fetchMethod` option.", "optional": true, "dependencies": {"lru-cache": "^4.0.0"}}, "node_modules/async-cache/node_modules/lru-cache": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "integrity": "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==", "optional": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/async-cache/node_modules/yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==", "optional": true}, "node_modules/async-value": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/async-value/-/async-value-1.2.2.tgz", "integrity": "sha512-8rwtYe32OAS1W9CTwvknoyts+mc3ta8N7Pi0h7AjkMaKvsFbr39K+gEfZ7Z81aPXQ1sK5M23lgLy1QfZpcpadQ==", "optional": true}, "node_modules/async-value-promise": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/async-value-promise/-/async-value-promise-1.1.1.tgz", "integrity": "sha512-c2RFDKjJle1rHa0YxN9Ysu97/QBu3Wa+NOejJxsX+1qVDJrkD3JL/GN1B3gaILAEXJXbu/4Z1lcoCHFESe/APA==", "optional": true, "dependencies": {"async-value": "^1.2.2"}}, "node_modules/atomic-sleep": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz", "integrity": "sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==", "optional": true, "engines": {"node": ">=8.0.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/base64-js/-/base64-js-1.3.1.tgz", "integrity": "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==", "license": "MIT"}, "node_modules/base64id": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/base64id/-/base64id-2.0.0.tgz", "integrity": "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==", "license": "MIT", "engines": {"node": "^4.5.0 || >= 5.9"}}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "optional": true, "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/binary-extensions": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/binary-extensions/-/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/binary-search": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/binary-search/-/binary-search-1.3.6.tgz", "integrity": "sha512-nbE1WxOTTrUWIfsfZ4aHGYu5DOuNkbxGokjV6Z2kxfJK3uaAb8zNK1muzOeipoLHZjInT4Br88BHpzevc681xA==", "optional": true}, "node_modules/bintrees": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/bintrees/-/bintrees-1.0.2.tgz", "integrity": "sha512-VOMgTMwjAaUG580SXn3LacVgjurrbMme7ZZNYGSSV7mmtY6QQRh0Eg3pwIcntQ77DErK1L0NxkbetjcoXzVwKw==", "license": "MIT"}, "node_modules/bl": {"version": "6.0.12", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/bl/-/bl-6.0.12.tgz", "integrity": "sha512-EnEYHilP93oaOa2MnmNEjAcovPS3JlQZOyzGXi3EyEpPhm9qWvdDp7BmAVEVusGzp8LlwQK56Av+OkDoRjzE0w==", "license": "MIT", "dependencies": {"@types/readable-stream": "^4.0.0", "buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}}, "node_modules/bl/node_modules/readable-stream": {"version": "4.5.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readable-stream/-/readable-stream-4.5.2.tgz", "integrity": "sha512-yjavECdqeZ3GLXNgRXgeQEdz9fvDDkNKyHnbHRFtOr7/LcfgBcmct7t/ET+HaCTqfh06OzoAxrkN/IfjJBVe+g==", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/bl/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "license": "MIT"}, "node_modules/bl/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/body-parser/node_modules/debug": {"version": "4.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/body-parser/node_modules/media-typer": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/mime-db": {"version": "1.54.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/body-parser/node_modules/mime-types": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/body-parser/node_modules/type-is": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-is/-/type-is-2.0.1.tgz", "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/breadth-filter": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/breadth-filter/-/breadth-filter-2.0.0.tgz", "integrity": "sha512-thQShDXnFWSk2oVBixRCyrWsFoV5tfOpWKHmxwafHQDNxCfDBk539utpvytNjmlFrTMqz41poLwJvA1MW3z0MQ==", "optional": true, "dependencies": {"object.entries": "^1.0.4"}}, "node_modules/brotli": {"version": "1.3.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/brotli/-/brotli-1.3.3.tgz", "integrity": "sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==", "license": "MIT", "dependencies": {"base64-js": "^1.1.2"}}, "node_modules/browser-stdout": {"version": "1.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/browser-stdout/-/browser-stdout-1.3.1.tgz", "integrity": "sha512-qhAVI1+Av2X7qelOfAIYwXONood6XlZE/fXaBSmW/T5SzLAmCgzi+eiWE7fUvbHaeNBQH13UftjpXxsfLkMpgw==", "dev": true, "license": "ISC"}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-more-ints": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/buffer-more-ints/-/buffer-more-ints-1.0.0.tgz", "integrity": "sha512-EMetuGFz5SLsT0QTnXzINh4Ksr+oo4i+UGTXEshiGCQWnsgSs7ZhJ8fzlwQ+OzEMs0MpDAMr1hxnblp5a4vcHg==", "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/c8": {"version": "10.1.3", "resolved": "https://registry.npmjs.org/c8/-/c8-10.1.3.tgz", "integrity": "sha512-LvcyrOAaOnrrlMpW22n690PUvxiq4Uf9WMhQwNJ9vgagkL/ph1+D4uvjvDA5XCbykrc0sx+ay6pVi9YZ1GnhyA==", "dev": true, "license": "ISC", "dependencies": {"@bcoe/v8-coverage": "^1.0.1", "@istanbuljs/schema": "^0.1.3", "find-up": "^5.0.0", "foreground-child": "^3.1.1", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-report": "^3.0.1", "istanbul-reports": "^3.1.6", "test-exclude": "^7.0.1", "v8-to-istanbul": "^9.0.0", "yargs": "^17.7.2", "yargs-parser": "^21.1.1"}, "bin": {"c8": "bin/c8.js"}, "engines": {"node": ">=18"}, "peerDependencies": {"monocart-coverage-reports": "^2"}, "peerDependenciesMeta": {"monocart-coverage-reports": {"optional": true}}}, "node_modules/c8/node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/c8/node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/c8/node_modules/glob": {"version": "10.4.5", "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/c8/node_modules/jackspeak": {"version": "3.4.3", "resolved": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/c8/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/c8/node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/c8/node_modules/test-exclude": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/test-exclude/-/test-exclude-7.0.1.tgz", "integrity": "sha512-pFYqmTw68LXVjeWJMST4+borgQP2AyMNbg1BpZh9LbyhUeNkeaPF9gzfPGUAnSMV3qPYdWUwDIjjCLiSDOl7vg==", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^10.4.1", "minimatch": "^9.0.4"}, "engines": {"node": ">=18"}}, "node_modules/c8/node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/c8/node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/call-bind": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/call-bind/-/call-bind-1.0.7.tgz", "integrity": "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==", "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chai": {"version": "5.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/chai/-/chai-5.2.0.tgz", "integrity": "sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/charenc": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz", "integrity": "sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/check-error": {"version": "2.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/check-error/-/check-error-2.1.1.tgz", "integrity": "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/chokidar": {"version": "3.5.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/chokidar/-/chokidar-3.5.3.tgz", "integrity": "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/cjs-module-lexer": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz", "integrity": "sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==", "optional": true}, "node_modules/cliui": {"version": "7.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cliui/-/cliui-7.0.4.tgz", "integrity": "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/clone/-/clone-2.1.2.tgz", "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/cluster-key-slot": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cluster-key-slot/-/cluster-key-slot-1.1.2.tgz", "integrity": "sha512-RMr0FhtfXemyinomL4hrWcYJxmX6deFdCxpJzhDttxgO1+bcCnkk+9drydLVDmAMG7NE6aN/fl4F7ucU/90gAA==", "license": "Apache-2.0", "engines": {"node": ">=0.10.0"}}, "node_modules/color": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/color/-/color-3.2.1.tgz", "integrity": "sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==", "dependencies": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/color/node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color/node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="}, "node_modules/colors": {"version": "1.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/colors/-/colors-1.4.0.tgz", "integrity": "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA==", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/colorspace": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/colorspace/-/colorspace-1.1.4.tgz", "integrity": "sha512-BgvKJiuVu1igBUF2kEjRCZXol6wiiGbY5ipL/oVPwm0BL9sIpMIzM8IK7vwuxIIzOXMV3Ey5w+vxhm0rR/TN8w==", "dependencies": {"color": "^3.1.3", "text-hex": "1.0.x"}}, "node_modules/commander": {"version": "11.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/commander/-/commander-11.1.0.tgz", "integrity": "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/console-log-level": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/console-log-level/-/console-log-level-1.4.1.tgz", "integrity": "sha512-VZzbIORbP+PPcN/gg3DXClTLPLg5Slwd5fL2MIc+o1qZ4BXBvWyc6QxPk6T/Mkr6IVjRpoAGf32XxP3ZWMVRcQ==", "optional": true}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-disposition/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true, "license": "MIT"}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cookie/-/cookie-1.0.1.tgz", "integrity": "sha512-Xd8lFX4LM9QEEwxQpF9J9NTUh8pmdJO0cyRJhFiDoLTk2eH8FXlRv2IFGYVadZpqI3j8fhNrSdKCeYPxiAhLXw==", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/cookie-parser": {"version": "1.4.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cookie-parser/-/cookie-parser-1.4.7.tgz", "integrity": "sha512-nGUvgXnotP3BsjiLX2ypbQnWoGUPIIfHQNZkkC668ntrzGWEZVW70HDEB1qnNGMicPje6EttlIgzo51YSwNQGw==", "license": "MIT", "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.6"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/copyfiles": {"version": "2.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/copyfiles/-/copyfiles-2.4.1.tgz", "integrity": "sha512-fereAvAvxDrQDOXybk3Qu3dPbOoKoysFMWtkY3mv5BsL8//OSZVL5DCLYqgRfY5cWirgRzlC+WSrxp6Bo3eNZg==", "dev": true, "license": "MIT", "dependencies": {"glob": "^7.0.5", "minimatch": "^3.0.3", "mkdirp": "^1.0.4", "noms": "0.0.0", "through2": "^2.0.1", "untildify": "^4.0.0", "yargs": "^16.1.0"}, "bin": {"copyfiles": "copyfiles", "copyup": "copyfiles"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/create-require/-/create-require-1.1.1.tgz", "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==", "dev": true, "license": "MIT"}, "node_modules/cross-env": {"version": "7.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cross-env/-/cross-env-7.0.3.tgz", "integrity": "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.1"}, "bin": {"cross-env": "src/bin/cross-env.js", "cross-env-shell": "src/bin/cross-env-shell.js"}, "engines": {"node": ">=10.14", "npm": ">=6", "yarn": ">=1"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypt": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz", "integrity": "sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/crypto-js": {"version": "4.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/crypto-js/-/crypto-js-4.2.0.tgz", "integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==", "license": "MIT"}, "node_modules/dayjs": {"version": "1.11.10", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.10.tgz", "integrity": "sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ=="}, "node_modules/debug": {"version": "4.3.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/debug/-/debug-4.3.5.tgz", "integrity": "sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/debug/node_modules/ms": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "license": "MIT"}, "node_modules/deep-eql": {"version": "5.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/deep-eql/-/deep-eql-5.0.1.tgz", "integrity": "sha512-nwQCf6ne2gez3o1MxWifqkciwt0zhl0LO1/UwVu4uMBuPmflWM4oQ70XMqHqnBJA+nhzncaqL9HVL6KkHJ28lw==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dfa": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/dfa/-/dfa-1.2.0.tgz", "integrity": "sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==", "license": "MIT"}, "node_modules/diff": {"version": "7.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/diff/-/diff-7.0.0.tgz", "integrity": "sha512-PJWHUb1RFevKCwaFA9RlG5tCd+FO5iRh9A8HEtkmBH2Li03iJriB6m6JIN4rGz3K3JLawI7/veA1xzRKP6ISBw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==", "dev": true, "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/elastic-apm-node": {"version": "3.51.0", "resolved": "https://registry.npmjs.org/elastic-apm-node/-/elastic-apm-node-3.51.0.tgz", "integrity": "sha512-GvZyoV4uhHB9qW4QE4pGcYZLbDCay2VzbeE5zN5v9vrQQ7j72GbzE5wGmtryNHwqP4DGCuXUk/jerArfpIquOQ==", "optional": true, "dependencies": {"@elastic/ecs-pino-format": "^1.2.0", "@opentelemetry/api": "^1.4.1", "@opentelemetry/core": "^1.11.0", "@opentelemetry/sdk-metrics": "^1.12.0", "after-all-results": "^2.0.0", "agentkeepalive": "^4.2.1", "async-cache": "^1.1.0", "async-value-promise": "^1.1.1", "basic-auth": "^2.0.1", "breadth-filter": "^2.0.0", "cookie": "^0.5.0", "core-util-is": "^1.0.2", "end-of-stream": "^1.4.4", "error-callsites": "^2.0.4", "error-stack-parser": "^2.0.6", "escape-string-regexp": "^4.0.0", "fast-safe-stringify": "^2.0.7", "fast-stream-to-buffer": "^1.0.0", "http-headers": "^3.0.2", "import-in-the-middle": "1.4.2", "is-native": "^1.0.1", "lru-cache": "^6.0.0", "measured-reporting": "^1.51.1", "module-details-from-path": "^1.0.3", "monitor-event-loop-delay": "^1.0.0", "object-filter-sequence": "^1.0.0", "object-identity-map": "^1.0.2", "original-url": "^1.2.3", "pino": "^6.11.2", "readable-stream": "^3.4.0", "relative-microtime": "^2.0.0", "require-in-the-middle": "^7.1.1", "semver": "^6.3.1", "shallow-clone-shim": "^2.0.0", "source-map": "^0.8.0-beta.0", "sql-summary": "^1.0.1", "stream-chopper": "^3.0.1", "unicode-byte-truncate": "^1.0.0"}, "engines": {"node": ">=8.6.0"}}, "node_modules/elastic-apm-node/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "optional": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/elastic-apm-node/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "optional": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/elastic-apm-node/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "optional": true}, "node_modules/elastic-apm-node/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "optional": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/elastic-apm-node/node_modules/source-map": {"version": "0.8.0-beta.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz", "integrity": "sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==", "optional": true, "dependencies": {"whatwg-url": "^7.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/elastic-apm-node/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "optional": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/elastic-apm-node/node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "optional": true}, "node_modules/emittery": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/emittery/-/emittery-1.0.3.tgz", "integrity": "sha512-tJdCJitoy2lrC2ldJcqN4vkqJ00lT+tOWNT1hBJjO/3FDMJa5TTIiYGCKGkn/WfCyOzUMObeohbVTj00fhiLiA==", "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/enabled": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/enabled/-/enabled-2.0.0.tgz", "integrity": "sha512-AKrN98kuwOzMIdAizXGI86UFBoo26CL21UM763y1h/GMSJ4/OHU9k2YlsmBpyScFo/wbLzWQJBMCW4+IO3/+OQ=="}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "optional": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/engine.io": {"version": "6.6.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/engine.io/-/engine.io-6.6.4.tgz", "integrity": "sha512-ZCkIjSYNDyGn0R6ewHDtXgns/Zre/NT6Agvq1/WobF7JXgFff4SeDroKiCO3fNJreU9YG429Sc81o4w5ok/W5g==", "license": "MIT", "dependencies": {"@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.7.2", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1"}, "engines": {"node": ">=10.2.0"}}, "node_modules/engine.io-parser": {"version": "5.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/engine.io-parser/-/engine.io-parser-5.2.3.tgz", "integrity": "sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/error-callsites": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/error-callsites/-/error-callsites-2.0.4.tgz", "integrity": "sha512-V877Ch4FC4FN178fDK1fsrHN4I1YQIBdtjKrHh3BUHMnh3SMvwUVrqkaOgDpUuevgSNna0RBq6Ox9SGlxYrigA==", "optional": true, "engines": {"node": ">=6.x"}}, "node_modules/error-stack-parser": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz", "integrity": "sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==", "optional": true, "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/es-abstract": {"version": "1.22.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-abstract/-/es-abstract-1.22.3.tgz", "integrity": "sha512-eiiY8HQeYfYH2Con2berK+To6GrK2RxbPawDkGq4UiCQQfZHb6wX9qQqkbpPqaxQFcl8d9QzZqo0tGE0VcrdwA==", "license": "MIT", "optional": true, "dependencies": {"array-buffer-byte-length": "^1.0.0", "arraybuffer.prototype.slice": "^1.0.2", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.5", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.6", "get-intrinsic": "^1.2.2", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.12", "is-weakref": "^1.0.2", "object-inspect": "^1.13.1", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.1", "safe-array-concat": "^1.0.1", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.8", "string.prototype.trimend": "^1.0.7", "string.prototype.trimstart": "^1.0.7", "typed-array-buffer": "^1.0.0", "typed-array-byte-length": "^1.0.0", "typed-array-byte-offset": "^1.0.0", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "integrity": "sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==", "license": "MIT", "optional": true, "dependencies": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "integrity": "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=", "license": "MIT", "optional": true, "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/escalade/-/escalade-3.1.2.tgz", "integrity": "sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "devOptional": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.29.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint/-/eslint-9.29.0.tgz", "integrity": "sha512-GsGizj2Y1rCWDu6XoEekL3RLilp0voSePurjZIkxL3wlm5o5EC9VpgaP7lrCvjnkuLvzFBQWB3vWB3K5KQTveQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.1", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.29.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-scope": {"version": "8.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint-scope/-/eslint-scope-8.4.0.tgz", "integrity": "sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esmock": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/esmock/-/esmock-2.7.0.tgz", "integrity": "sha512-nYSvky0rP//13eYIGJ7b1dhsaBxtS370UoDWbILo1jHlO4hrCUkp/LD3e54QVhySxAGLsufxi5A4+wUAxPUIrg==", "dev": true, "license": "ISC", "engines": {"node": ">=14.16.0"}}, "node_modules/espree": {"version": "10.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/espree/-/espree-10.4.0.tgz", "integrity": "sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree/node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.5.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/esquery/-/esquery-1.5.0.tgz", "integrity": "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/excel4node": {"version": "1.8.2", "resolved": "https://registry.npmjs.org/excel4node/-/excel4node-1.8.2.tgz", "integrity": "sha512-v5BZZy8y4cibFQ/xvztUleAoyYmIBol1qTKWuDWZZPpFGBAy4P7qkswdpBkTkQgLIQ/WkCpyV/P6liW4mIb/wQ==", "dependencies": {"deepmerge": "^4.2.2", "image-size": "^1.0.2", "jszip": "^3.10.0", "lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "lodash.isundefined": "^3.0.1", "lodash.reduce": "^4.6.0", "lodash.uniqueid": "^4.0.1", "mime": "^3.0.0", "uuid": "^9.0.0", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=14.0.0"}}, "node_modules/excel4node/node_modules/mime": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/mime/-/mime-3.0.0.tgz", "integrity": "sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==", "bin": {"mime": "cli.js"}, "engines": {"node": ">=10.0.0"}}, "node_modules/excel4node/node_modules/uuid": {"version": "9.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uuid/-/uuid-9.0.1.tgz", "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/express": {"version": "5.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/express/-/express-5.1.0.tgz", "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/accepts": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/accepts/-/accepts-2.0.0.tgz", "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/express/node_modules/debug": {"version": "4.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/express/node_modules/fresh": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fresh/-/fresh-2.0.0.tgz", "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/media-typer": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/express/node_modules/mime-db": {"version": "1.54.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/mime-types": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/negotiator": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "license": "MIT"}, "node_modules/express/node_modules/type-is": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-is/-/type-is-2.0.1.tgz", "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fast-redact": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/fast-redact/-/fast-redact-3.3.0.tgz", "integrity": "sha512-6T5V1QK1u4oF+ATxs1lWUmlEk6P2T9HqJG3e2DnHOdVgZy2rFJBoEnrIedcTXlkAHU/zKC+7KETJ+KGGKwxgMQ==", "optional": true, "engines": {"node": ">=6"}}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "integrity": "sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==", "optional": true}, "node_modules/fast-stream-to-buffer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fast-stream-to-buffer/-/fast-stream-to-buffer-1.0.0.tgz", "integrity": "sha512-bI/544WUQlD2iXBibQbOMSmG07Hay7YrpXlKaeGTPT7H7pC0eitt3usak5vUwEvCGK/O7rUAM3iyQValGU22TQ==", "optional": true, "dependencies": {"end-of-stream": "^1.4.1"}}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fastq/-/fastq-1.19.1.tgz", "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fecha": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/fecha/-/fecha-4.2.3.tgz", "integrity": "sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw=="}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "4.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/find-up/-/find-up-5.0.0.tgz", "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat": {"version": "5.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/flat/-/flat-5.0.2.tgz", "integrity": "sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatstr": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/flatstr/-/flatstr-1.0.12.tgz", "integrity": "sha512-4zPxDyhCyiN2wIAtSLI6gc82/EjqZc1onI4Mz/l0pWrAlsSfYH/2ZIcU+e3oA2wDwbzIWNKwa23F8rh6+DRWkw==", "optional": true}, "node_modules/flatted": {"version": "3.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/flatted/-/flatted-3.3.1.tgz", "integrity": "sha512-X8c<PERSON>MLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==", "dev": true, "license": "ISC"}, "node_modules/fn.name": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fn.name/-/fn.name-1.1.0.tgz", "integrity": "sha512-GRnmB5gPyJpAhTQdSZTSp9uaPSvl09KoYcMQtsB9rQoOmzs9dH6ffeccH+Z+cv6P68Hu5bC6JjRh4Ah/mHSNRw=="}, "node_modules/fontkit": {"version": "2.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fontkit/-/fontkit-2.0.4.tgz", "integrity": "sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.12", "brotli": "^1.3.2", "clone": "^2.1.2", "dfa": "^1.2.0", "fast-deep-equal": "^3.1.3", "restructure": "^3.0.0", "tiny-inflate": "^1.0.3", "unicode-properties": "^1.4.0", "unicode-trie": "^2.0.0"}}, "node_modules/for-each": {"version": "0.3.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/for-each/-/for-each-0.3.3.tgz", "integrity": "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==", "license": "MIT", "optional": true, "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/foreground-child/-/foreground-child-3.3.1.tgz", "integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/forwarded-parse": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/forwarded-parse/-/forwarded-parse-2.1.2.tgz", "integrity": "sha512-alTFZZQDKMporBH77856pXgzhEzaUVmLCDk+egLgIgHst3Tpndzz8MnKe+GzRJRfvVdn69HhpW7cmXzvtLvJAw==", "optional": true}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/function.prototype.name/-/function.prototype.name-1.1.6.tgz", "integrity": "sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=", "license": "MIT", "optional": true}, "node_modules/generic-pool": {"version": "3.9.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/generic-pool/-/generic-pool-3.9.0.tgz", "integrity": "sha512-hymDOu5B53XvN4QT9dBmZxPX4CWhBPPLguTZ9MMFeFa/Kg0xWVfylOVNlJji/E7yTZWFd/q9GO5TxDLq156D7g==", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-func-name": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/get-func-name/-/get-func-name-2.0.2.tgz", "integrity": "sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "integrity": "sha1-f9uByQAQH71WTdXxowr1qtweWNY=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "16.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/globals/-/globals-16.2.0.tgz", "integrity": "sha512-O+7l9tPdHCU320IigZZPj5zmRCFG9xHmx9cU8FqU2Rp+JN714seHV+2S9+JslCpY4gJwU2vOGox0wzgae/MCEg==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/globalthis/-/globalthis-1.0.3.tgz", "integrity": "sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==", "license": "MIT", "optional": true, "dependencies": {"define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==", "dev": true, "license": "MIT"}, "node_modules/has": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has/-/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "license": "MIT", "optional": true, "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-bigints/-/has-bigints-1.0.2.tgz", "integrity": "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=", "license": "MIT", "optional": true}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-proto/-/has-proto-1.0.1.tgz", "integrity": "sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "integrity": "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=", "devOptional": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hpagent": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/hpagent/-/hpagent-0.1.2.tgz", "integrity": "sha512-ePqFXHtSQWAFXYmj+JtOTHr84iNrII4/QRlAAPPE+zqnKy4xJo7Ie1Y4kC7AdB+LxLxSTTzBMASsEcy0q8YyvQ=="}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-headers": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/http-headers/-/http-headers-3.0.2.tgz", "integrity": "sha512-87E1I+2Wg4dxxz4rcxElo3dxO/w1ZtgL1yA0Sb6vH3qU16vRKq1NjWQv9SCY3ly2OQROcoxHZOUpmelS+k6wOw==", "optional": true, "dependencies": {"next-line": "^1.1.0"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/http-status": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/http-status/-/http-status-2.1.0.tgz", "integrity": "sha512-O5kPr7AW7wYd/BBiOezTwnVAnmSNFY+J7hlZD2X5IOxVBetjcHAiTXhzj0gMrnojQlwy+UT1/Y3H3vJ3UlmvLA==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 0.4.0"}}, "node_modules/https-proxy-agent": {"version": "7.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/https-proxy-agent/-/https-proxy-agent-7.0.4.tgz", "integrity": "sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==", "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/humanize-ms": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz", "integrity": "sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==", "optional": true, "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ignore/-/ignore-5.3.2.tgz", "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/ignore-by-default": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ignore-by-default/-/ignore-by-default-1.0.1.tgz", "integrity": "sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==", "dev": true, "license": "ISC"}, "node_modules/image-size": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/image-size/-/image-size-1.2.1.tgz", "integrity": "sha512-rH+46sQJ2dlwfjfhCyNx5thzrv+dtmBIhPHk0zgRUukHzZ/kRueTJXoYYsclBaKcSMBWuGbOFXtioLpzTb5euw==", "license": "MIT", "dependencies": {"queue": "6.0.2"}, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=16.x"}}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-in-the-middle": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/import-in-the-middle/-/import-in-the-middle-1.4.2.tgz", "integrity": "sha512-9WOz1Yh/cvO/p69sxRmhyQwrIGGSp7EIdcb+fFNVi7CzQGQB8U1/1XrKVSbEd/GNOAeM0peJtmi7+qphe7NvAw==", "optional": true, "dependencies": {"acorn": "^8.8.2", "acorn-import-assertions": "^1.9.0", "cjs-module-lexer": "^1.2.2", "module-details-from-path": "^1.0.3"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/internal-slot": {"version": "1.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/internal-slot/-/internal-slot-1.0.5.tgz", "integrity": "sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==", "license": "MIT", "optional": true, "dependencies": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-array-buffer": {"version": "3.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-array-buffer/-/is-array-buffer-3.0.2.tgz", "integrity": "sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="}, "node_modules/is-bigint": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-bigint/-/is-bigint-1.0.4.tgz", "integrity": "sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=", "license": "MIT", "optional": true, "dependencies": {"has-bigints": "^1.0.1"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "integrity": "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true, "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/is-core-module": {"version": "2.13.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-core-module/-/is-core-module-2.13.1.tgz", "integrity": "sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==", "license": "MIT", "optional": true, "dependencies": {"hasown": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-date-object/-/is-date-object-1.0.5.tgz", "integrity": "sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=", "license": "MIT", "optional": true, "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-docker": {"version": "2.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finite": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz", "integrity": "sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==", "optional": true, "engines": {"node": ">=0.10.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-integer": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/is-integer/-/is-integer-1.0.7.tgz", "integrity": "sha512-RPQc/s9yBHSvpi+hs9dYiJ2cuFeU6x3TyyIp8O2H6SKEltIvJOzRj9ToyvcStDvPR/pS4rxgr1oBFajQjZ2Szg==", "optional": true, "dependencies": {"is-finite": "^1.0.0"}}, "node_modules/is-native": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-native/-/is-native-1.0.1.tgz", "integrity": "sha512-I4z9hx+4u3/zyvpvGtAR+n7SodJugE+i2jiS8yfq1A9QAZY0KldLQz0SBptLC9ti7kBlpghWUwTKE2BA62eCcw==", "optional": true, "dependencies": {"is-nil": "^1.0.0", "to-source-code": "^1.0.0"}}, "node_modules/is-negative-zero": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/is-nil": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-nil/-/is-nil-1.0.1.tgz", "integrity": "sha512-m2Rm8PhUFDNNhgvwZJjJG74a9h5CHU0fkA8WT+WGlCjyEbZ2jPwgb+ZxHu4np284EqNVyOsgppReK4qy/TwEwg==", "optional": true}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-number-object/-/is-number-object-1.0.7.tgz", "integrity": "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=", "license": "MIT", "optional": true, "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "integrity": "sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==", "license": "MIT"}, "node_modules/is-regex": {"version": "1.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "integrity": "sha1-jyWcVztgtqMtQFihoHQwwKc0THk=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-string": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-string/-/is-string-1.0.7.tgz", "integrity": "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=", "devOptional": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-symbol": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-symbol/-/is-symbol-1.0.4.tgz", "integrity": "sha1-ptrJO2NbBjymhyI23oiRClevE5w=", "license": "MIT", "optional": true, "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-typed-array": {"version": "1.1.12", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-typed-array/-/is-typed-array-1.1.12.tgz", "integrity": "sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==", "license": "MIT", "optional": true, "dependencies": {"which-typed-array": "^1.1.11"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-weakref": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-weakref/-/is-weakref-1.0.2.tgz", "integrity": "sha1-lSnzg6kzggXol2XgOS78LxAPBvI=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2"}}, "node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "0.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.0.tgz", "integrity": "sha512-eOeJ5BHCmHYvQK7xt9GkdHuzuCGS1Y6g9Gvnx3Ym33fz/HpLRYxiS0wHNr+m/MBC8B647Xt608vCDEvhl9c6Mw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report/node_modules/make-dir": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "resolved": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jackspeak": {"version": "4.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jackspeak/-/jackspeak-4.0.1.tgz", "integrity": "sha512-cub8rahkh0Q/bw1+GxP7aeSe29hHHn2V4m29nnDlvCdlgU+3UGxkZp7Z53jLUdpX3jdTO0nJZUDl3xvbWc2Xog==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jpeg-exif": {"version": "1.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jpeg-exif/-/jpeg-exif-1.1.4.tgz", "integrity": "sha512-a+bKEcCjtuW5WTdgeXFzswSrdqi0jk4XlEtZlx5A94wCoBpFjfFTbo/Tra5SpNCl/YFZPvcV1dJc+TAYeg6ROQ==", "license": "MIT"}, "node_modules/js-md4": {"version": "0.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/js-md4/-/js-md4-0.3.2.tgz", "integrity": "sha512-/GDnfQYsltsjRswQhN9fhv3EMw2sCpUdrdxyWDOUK7eyD++r3gRhzgiQgc/x4MAv2i1iuQ4lxO5mvqM3vj4bwA==", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jsonwebtoken/node_modules/jwa": {"version": "1.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jwa/-/jwa-1.4.1.tgz", "integrity": "sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jsonwebtoken/node_modules/jws": {"version": "3.2.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jws/-/jws-3.2.2.tgz", "integrity": "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/jszip": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz", "integrity": "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/jszip/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="}, "node_modules/jszip/node_modules/pako": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="}, "node_modules/jszip/node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/jszip/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/jwa": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jwa/-/jwa-2.0.0.tgz", "integrity": "sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jws/-/jws-4.0.0.tgz", "integrity": "sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==", "license": "MIT", "dependencies": {"jwa": "^2.0.0", "safe-buffer": "^5.0.1"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/keyv/-/keyv-4.5.4.tgz", "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kuler": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/kuler/-/kuler-2.0.0.tgz", "integrity": "sha512-Xq9nH7KlWZmXAtodXDDRE7vs6DU1gTU8zYDHDiWLSip45Egwq3plLHzPn27NgvzL2r1LMPC1vdqh98sQxtqj4A=="}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lie": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/linebreak": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/linebreak/-/linebreak-1.1.0.tgz", "integrity": "sha512-MHp03UImeVhB7XZtjd0E4n6+3xr5Dq/9xI/5FptGk5FrbDR3zagPa2DS6U8ks/3HjbKWG9Q1M2ufOzxV2qLYSQ==", "license": "MIT", "dependencies": {"base64-js": "0.0.8", "unicode-trie": "^2.0.0"}}, "node_modules/linebreak/node_modules/base64-js": {"version": "0.0.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/base64-js/-/base64-js-0.0.8.tgz", "integrity": "sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash.defaults": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "integrity": "sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ=="}, "node_modules/lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ=="}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ=="}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==", "license": "MIT"}, "node_modules/lodash.isundefined": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash.isundefined/-/lodash.isundefined-3.0.1.tgz", "integrity": "sha512-MXB1is3s899/cD8jheYYE2V9qTHwKvt+npCwpD+1Sxm3Q3cECXCiYHjeHWXNwr6Q0SOBPrYUDxendrO6goVTEA=="}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "devOptional": true, "license": "MIT"}, "node_modules/lodash.omit": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.omit/-/lodash.omit-4.5.0.tgz", "integrity": "sha512-XeqSp49hNGmlkj2EJlfrQFIzQ6lXdNro9sddtQzcJY8QaoC2GO0DT7xaIokHeyM+mIT0mPMlPvkYzg2xCuHdZg=="}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==", "license": "MIT"}, "node_modules/lodash.reduce": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/lodash.reduce/-/lodash.reduce-4.6.0.tgz", "integrity": "sha512-6raRe2vxCYBhpBu+B+TtNGUzah+hQjVdu3E17wfusjyrXBka2nBS8OH/gjVZ5PvHOhWmIZTYri09Z6n/QfnNMw=="}, "node_modules/lodash.sortby": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz", "integrity": "sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==", "optional": true}, "node_modules/lodash.uniqueid": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/lodash.uniqueid/-/lodash.uniqueid-4.0.1.tgz", "integrity": "sha512-GQQWaIeGlL6DIIr06kj1j6sSmBxyNMwI8kaX9aKpHR/XsMTiaXDVPNPAkiboOTK9OJpTJF/dXT3xYoFQnj386Q=="}, "node_modules/log-symbols": {"version": "4.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/logform": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/logform/-/logform-2.7.0.tgz", "integrity": "sha512-TFYA4jnP7PVbmlBIfhlSe+WKxs9dklXMTEGcBCIvLhE/Tn3H6Gk1norupVW7m5Cnd4bLcr08AytbyV/xj7f/kQ==", "license": "MIT", "dependencies": {"@colors/colors": "1.6.0", "@types/triple-beam": "^1.3.2", "fecha": "^4.2.0", "ms": "^2.1.1", "safe-stable-stringify": "^2.3.1", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/loupe": {"version": "3.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/loupe/-/loupe-3.1.1.tgz", "integrity": "sha512-edNu/8D5MKVfGVFRhFf8aAxiTM6Wumfz5XsaatSxlD3w4R1d/WEKUTydCdPGbl9K7QG/Ca3GnDV2sIKIpXRQcw==", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.1"}}, "node_modules/lru-cache": {"version": "10.4.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "dev": true, "license": "ISC"}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/make-error/-/make-error-1.3.6.tgz", "integrity": "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==", "dev": true, "license": "ISC"}, "node_modules/mapcap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mapcap/-/mapcap-1.0.0.tgz", "integrity": "sha512-KcNlZSlFPx+r1jYZmxEbTVymG+dIctf10WmWkuhrhrblM+KMoF77HelwihL5cxYlORye79KoR4IlOOk99lUJ0g==", "optional": true}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz", "integrity": "sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "node_modules/measured-core": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/measured-core/-/measured-core-1.51.1.tgz", "integrity": "sha512-DZQP9SEwdqqYRvT2slMK81D/7xwdxXosZZBtLVfPSo6y5P672FBTbzHVdN4IQyUkUpcVOR9pIvtUy5Ryl7NKyg==", "optional": true, "dependencies": {"binary-search": "^1.3.3", "optional-js": "^2.0.0"}, "engines": {"node": ">= 5.12"}}, "node_modules/measured-reporting": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/measured-reporting/-/measured-reporting-1.51.1.tgz", "integrity": "sha512-JCt+2u6XT1I5lG3SuYqywE0e62DJuAzBcfMzWGUhIYtPQV2Vm4HiYt/durqmzsAbZV181CEs+o/jMKWJKkYIWw==", "optional": true, "dependencies": {"console-log-level": "^1.4.1", "mapcap": "^1.0.0", "measured-core": "^1.51.1", "optional-js": "^2.0.0"}, "engines": {"node": ">= 5.12"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minipass/-/minipass-7.1.2.tgz", "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mocha": {"version": "11.7.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mocha/-/mocha-11.7.0.tgz", "integrity": "sha512-bXfLy/mI8n4QICg+pWj1G8VduX5vC0SHRwFpiR5/Fxc8S2G906pSfkyMmHVsdJNQJQNh3LE67koad9GzEvkV6g==", "dev": true, "license": "MIT", "dependencies": {"browser-stdout": "^1.3.1", "chokidar": "^4.0.1", "debug": "^4.3.5", "diff": "^7.0.0", "escape-string-regexp": "^4.0.0", "find-up": "^5.0.0", "glob": "^10.4.5", "he": "^1.2.0", "js-yaml": "^4.1.0", "log-symbols": "^4.1.0", "minimatch": "^9.0.5", "ms": "^2.1.3", "picocolors": "^1.1.1", "serialize-javascript": "^6.0.2", "strip-json-comments": "^3.1.1", "supports-color": "^8.1.1", "workerpool": "^9.2.0", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yargs-unparser": "^2.0.0"}, "bin": {"_mocha": "bin/_mocha", "mocha": "bin/mocha.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/mocha-junit-reporter": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/mocha-junit-reporter/-/mocha-junit-reporter-2.2.1.tgz", "integrity": "sha512-iDn2tlKHn8Vh8o4nCzcUVW4q7iXp7cC4EB78N0cDHIobLymyHNwe0XG8HEHHjc3hJlXm0Vy6zcrxaIhnI2fWmw==", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "md5": "^2.3.0", "mkdirp": "^3.0.0", "strip-ansi": "^6.0.1", "xml": "^1.0.1"}, "peerDependencies": {"mocha": ">=2.2.5"}}, "node_modules/mocha-junit-reporter/node_modules/mkdirp": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "integrity": "sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha-multi": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/mocha-multi/-/mocha-multi-1.1.7.tgz", "integrity": "sha512-SXZRgHy0XiRTASyOp0p6fjOkdj+R62L6cqutnYyQOvIjNznJuUwzykxctypeRiOwPd+gfn4yt3NRulMQyI8Tzg==", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "is-string": "^1.0.4", "lodash.once": "^4.1.1", "mkdirp": "^1.0.4", "object-assign": "^4.1.1"}, "engines": {"node": ">=6.0.0"}, "peerDependencies": {"mocha": ">=2.2.0 <7 || >=9"}}, "node_modules/mocha/node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/mocha/node_modules/chokidar": {"version": "4.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/chokidar/-/chokidar-4.0.3.tgz", "integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "dev": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/mocha/node_modules/cliui": {"version": "8.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/mocha/node_modules/glob": {"version": "10.4.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob/-/glob-10.4.5.tgz", "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha/node_modules/jackspeak": {"version": "3.4.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/jackspeak/-/jackspeak-3.4.3.tgz", "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/mocha/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha/node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/mocha/node_modules/readdirp": {"version": "4.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readdirp/-/readdirp-4.1.2.tgz", "integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==", "dev": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/mocha/node_modules/supports-color": {"version": "8.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/mocha/node_modules/yargs": {"version": "17.7.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/mocha/node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/module-details-from-path": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/module-details-from-path/-/module-details-from-path-1.0.3.tgz", "integrity": "sha512-ySViT69/76t8VhE1xXHK6Ch4NcDd26gx0MzKXLO+F7NOtnqH68d9zF94nT8ZWSxXh8ELOERsnJO/sWt1xZYw5A==", "optional": true}, "node_modules/moment": {"version": "2.29.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.6.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/moment-timezone/-/moment-timezone-0.6.0.tgz", "integrity": "sha512-ldA5lRNm3iJCWZcBCab4pnNL3HSZYXVb/3TYr75/1WCTWYuTqYUb5f/S384pncYjJ88lbO8Z4uPDvmoluHJc8Q==", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/monitor-event-loop-delay": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/monitor-event-loop-delay/-/monitor-event-loop-delay-1.0.0.tgz", "integrity": "sha512-YRIr1exCIfBDLZle8WHOfSo7Xg3M+phcZfq9Fx1L6Abo+atGp7cge5pM7PjyBn4s1oZI/BRD4EMrzQBbPpVb5Q==", "optional": true}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/mssql": {"version": "11.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mssql/-/mssql-11.0.1.tgz", "integrity": "sha512-KlGNsugoT90enKlR8/G36H0kTxPthDhmtNUCwEHvgRza5Cjpjoj+P2X6eMpFUDN7pFrJZsKadL4x990G8RBE1w==", "license": "MIT", "dependencies": {"@tediousjs/connection-string": "^0.5.0", "commander": "^11.0.0", "debug": "^4.3.3", "rfdc": "^1.3.0", "tarn": "^3.0.2", "tedious": "^18.2.1"}, "bin": {"mssql": "bin/mssql"}, "engines": {"node": ">=18"}}, "node_modules/native-duplexpair": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/native-duplexpair/-/native-duplexpair-1.0.0.tgz", "integrity": "sha512-E7QQoM+3jvNtlmyfqRZ0/U75VFgCls+fSkbml2MpgWkWyz3ox8Y58gNhfuziuQYGNNQAbFZJQck55LHCnCK6CA==", "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/next-line": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/next-line/-/next-line-1.1.0.tgz", "integrity": "sha512-+I10J3wKNoKddNxn0CNpoZ3eTZuqxjNM3b1GImVx22+ePI+Y15P8g/j3WsbP0fhzzrFzrtjOAoq5NCCucswXOQ==", "optional": true}, "node_modules/node-mocks-http": {"version": "1.17.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/node-mocks-http/-/node-mocks-http-1.17.2.tgz", "integrity": "sha512-HVxSnjNzE9NzoWMx9T9z4MLqwMpLwVvA0oVZ+L+gXskYXEJ6tFn3Kx4LargoB6ie7ZlCLplv7QbWO6N+MysWGA==", "dev": true, "license": "MIT", "dependencies": {"accepts": "^1.3.7", "content-disposition": "^0.5.3", "depd": "^1.1.0", "fresh": "^0.5.2", "merge-descriptors": "^1.0.1", "methods": "^1.1.2", "mime": "^1.3.4", "parseurl": "^1.3.3", "range-parser": "^1.2.0", "type-is": "^1.6.18"}, "engines": {"node": ">=14"}, "peerDependencies": {"@types/express": "^4.17.21 || ^5.0.0", "@types/node": "*"}, "peerDependenciesMeta": {"@types/express": {"optional": true}, "@types/node": {"optional": true}}}, "node_modules/node-mocks-http/node_modules/depd": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nodemon": {"version": "3.1.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/nodemon/-/nodemon-3.1.10.tgz", "integrity": "sha512-WDjw3pJ0/0jMFmyNDp3gvY2YizjLmmOUQo6DEBY+JgdvW/yQ9mEeSw6H5ythl5Ny2ytb7f9C2nIbjSxMNzbJXw==", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nodemon/node_modules/has-flag": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/nodemon/node_modules/supports-color": {"version": "5.5.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/noms": {"version": "0.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/noms/-/noms-0.0.0.tgz", "integrity": "sha512-lNDU9VJaOPxUmXcLb+HQFeUgQQPtMI24Gt6hgfuMHRJgMRHMF/qZ4HJD3GDru4sSw9IQl2jPjAYnQrdIeLbwow==", "dev": true, "license": "ISC", "dependencies": {"inherits": "^2.0.1", "readable-stream": "~1.0.31"}}, "node_modules/noms/node_modules/readable-stream": {"version": "1.0.34", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/nopt": {"version": "1.0.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/nopt/-/nopt-1.0.10.tgz", "integrity": "sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==", "dev": true, "license": "MIT", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "*"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-filter-sequence": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/object-filter-sequence/-/object-filter-sequence-1.0.0.tgz", "integrity": "sha512-CsubGNxhIEChNY4cXYuA6KXafztzHqzLLZ/y3Kasf3A+sa3lL9thq3z+7o0pZqzEinjXT6lXDPAfVWI59dUyzQ==", "optional": true}, "node_modules/object-identity-map": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/object-identity-map/-/object-identity-map-1.0.2.tgz", "integrity": "sha512-a2XZDGyYTngvGS67kWnqVdpoaJWsY7C1GhPJvejWAFCsUioTAaiTu8oBad7c6cI4McZxr4CmvnZeycK05iav5A==", "optional": true, "dependencies": {"object.entries": "^1.1.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.entries": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.7.tgz", "integrity": "sha512-jCBs/0plmPsOnrKAfFQXRG2NFjlhZgjjcBLSmTnEhU8U6vVTsVe8ANeQJCHTl3gSsI4J+0emOoCgoKlmQPMgmA==", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/one-time": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/one-time/-/one-time-1.0.0.tgz", "integrity": "sha512-5DXOiRKwuSEcQ/l0kGCF6Q3jcADFv5tSmRaJck/OqkVFcOzutB134KRSfF0xDrL39MNnqxbHBbUUcjZIhTgb2g==", "dependencies": {"fn.name": "1.x.x"}}, "node_modules/open": {"version": "8.4.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/open/-/open-8.4.2.tgz", "integrity": "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optional-js": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/optional-js/-/optional-js-2.3.0.tgz", "integrity": "sha512-B0LLi+Vg+eko++0z/b8zIv57kp7HKEzaPJo7LowJXMUKYdf+3XJGu/cw03h/JhIOsLnP+cG5QnTHAuicjA5fMw==", "optional": true}, "node_modules/optionator": {"version": "0.9.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/optionator/-/optionator-0.9.3.tgz", "integrity": "sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==", "dev": true, "license": "MIT", "dependencies": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/original-url": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/original-url/-/original-url-1.2.3.tgz", "integrity": "sha512-BYm+pKYLtS4mVe/mgT3YKGtWV5HzN/XKiaIu1aK4rsxyjuHeTW9N+xVBEpJcY1onB3nccfH0RbzUEoimMqFUHQ==", "optional": true, "dependencies": {"forwarded-parse": "^2.1.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/package-json-from-dist": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/package-json-from-dist/-/package-json-from-dist-1.0.0.tgz", "integrity": "sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/pako": {"version": "0.2.9", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/pako/-/pako-0.2.9.tgz", "integrity": "sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==", "license": "MIT"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "license": "MIT", "optional": true}, "node_modules/path-scurry": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-scurry/-/path-scurry-2.0.0.tgz", "integrity": "sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg==", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^11.0.0", "minipass": "^7.1.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "11.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/lru-cache/-/lru-cache-11.0.0.tgz", "integrity": "sha512-Qv32eSV1RSCfhY3fpPE2GNZ8jgM9X7rdAfemLWqTUxwiyIC4jJ6Sy0fZ8H+oLWevO6i4/bizg7c8d8i6bxrzbA==", "dev": true, "license": "ISC", "engines": {"node": "20 || >=22"}}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/pathval": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/pathval/-/pathval-2.0.0.tgz", "integrity": "sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==", "dev": true, "license": "MIT", "engines": {"node": ">= 14.16"}}, "node_modules/pdfkit": {"version": "0.17.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/pdfkit/-/pdfkit-0.17.1.tgz", "integrity": "sha512-Kkf1I9no14O/uo593DYph5u3QwiMfby7JsBSErN1WqeyTgCBNJE3K4pXBn3TgkdKUIVu+buSl4bYUNC+8Up4xg==", "license": "MIT", "dependencies": {"crypto-js": "^4.2.0", "fontkit": "^2.0.4", "jpeg-exif": "^1.1.4", "linebreak": "^1.1.0", "png-js": "^1.0.0"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}}, "node_modules/pino": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/pino/-/pino-6.14.0.tgz", "integrity": "sha512-iuhEDel3Z3hF9Jfe44DPXR8l07bhjuFY3GMHIXbjnY9XcafbyDDwl2sN2vw2GjMPf5Nkoe+OFao7ffn9SXaKDg==", "optional": true, "dependencies": {"fast-redact": "^3.0.0", "fast-safe-stringify": "^2.0.8", "flatstr": "^1.0.12", "pino-std-serializers": "^3.1.0", "process-warning": "^1.0.0", "quick-format-unescaped": "^4.0.3", "sonic-boom": "^1.0.2"}, "bin": {"pino": "bin.js"}}, "node_modules/pino-std-serializers": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-3.2.0.tgz", "integrity": "sha512-EqX4pwDPrt3MuOAAUBMU0Tk5kR/YcCM5fNPEzgCO2zJ5HfX0vbiH9HbJglnyeQsN96Kznae6MWD47pZB5avTrg==", "optional": true}, "node_modules/png-js": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/png-js/-/png-js-1.0.0.tgz", "integrity": "sha512-k+YsbhpA9e+EFfKjTCH3VW6aoKlyNYI6NYdTfDL4CIvFnvsuO84ttonmZE7rc+v23SLTH8XX+5w/Ak9v0xGY4g=="}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettyjson": {"version": "1.2.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/prettyjson/-/prettyjson-1.2.5.tgz", "integrity": "sha512-rksPWtoZb2ZpT5OVgtmy0KHVM+Dca3iVwWY9ifwhcexfjebtgjg3wmrUt9PvJ59XIYBcknQeYHD8IAnVlh9lAw==", "license": "MIT", "dependencies": {"colors": "1.4.0", "minimist": "^1.2.0"}, "bin": {"prettyjson": "bin/pretty<PERSON>son"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "license": "MIT"}, "node_modules/process-warning": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/process-warning/-/process-warning-1.0.0.tgz", "integrity": "sha512-du4wfLyj4yCZq1VupnVSZmRsPJsNuxoDQFdCFHLaYiEbFBD7QE0a+I4D7hOxrVnh78QE/YipFAj9lXHiXocV+Q==", "optional": true}, "node_modules/prom-client": {"version": "15.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/prom-client/-/prom-client-15.1.3.tgz", "integrity": "sha512-6ZiOBfCywsD4k1BN9IX0uZhF+tJkV8q8llP64G5Hajs4JOeVLPCwpPVcpXy3BwYiUGgyJzsJJQeOIv7+hDSq8g==", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api": "^1.4.0", "tdigest": "^0.1.1"}, "engines": {"node": "^16 || ^18 || >=20"}}, "node_modules/promise": {"version": "8.3.0", "resolved": "https://registry.npmjs.org/promise/-/promise-8.3.0.tgz", "integrity": "sha512-rZPNPKTOYVNEEKFaq1HqTgOwZD+4/YHS5ukLzQCypkj+OkYx7iv0mA91lJlpPPZ8vMau3IIGj5Qlwrx+8iiSmg==", "dependencies": {"asap": "~2.0.6"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==", "optional": true}, "node_modules/pstree.remy": {"version": "1.1.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/pstree.remy/-/pstree.remy-1.1.8.tgz", "integrity": "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==", "dev": true, "license": "MIT"}, "node_modules/punycode": {"version": "2.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/punycode/-/punycode-2.3.0.tgz", "integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==", "devOptional": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystringify": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/querystringify/-/querystringify-2.2.0.tgz", "integrity": "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=", "license": "MIT"}, "node_modules/queue": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz", "integrity": "sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==", "dependencies": {"inherits": "~2.0.3"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-format-unescaped": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz", "integrity": "sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==", "optional": true}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/redis": {"version": "5.5.6", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/redis/-/redis-5.5.6.tgz", "integrity": "sha512-hbpqBfcuhWHOS9YLNcXcJ4akNr7HFX61Dq3JuFZ9S7uU7C7kvnzuH2PDIXOP62A3eevvACoG8UacuXP3N07xdg==", "license": "MIT", "dependencies": {"@redis/bloom": "5.5.6", "@redis/client": "5.5.6", "@redis/json": "5.5.6", "@redis/search": "5.5.6", "@redis/time-series": "5.5.6"}, "engines": {"node": ">= 18"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/regexp.prototype.flags/-/regexp.prototype.flags-1.5.1.tgz", "integrity": "sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "set-function-name": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/relative-microtime": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/relative-microtime/-/relative-microtime-2.0.0.tgz", "integrity": "sha512-l18ha6HEZc+No/uK4GyAnNxgKW7nvEe35IaeN54sShMojtqik2a6GbTyuiezkjpPaqP874Z3lW5ysBo5irz4NA==", "optional": true}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-in-the-middle": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/require-in-the-middle/-/require-in-the-middle-7.2.0.tgz", "integrity": "sha512-3TLx5TGyAY6AOqLBoXmHkNql0HIf2RGbuMgCDT2WO/uGVAPJs6h7Kl+bN6TIZGd9bWhWPwnDnTHGtW8Iu77sdw==", "optional": true, "dependencies": {"debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1"}, "engines": {"node": ">=8.6.0"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/resolve/-/resolve-1.22.8.tgz", "integrity": "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==", "license": "MIT", "optional": true, "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/response-time": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/response-time/-/response-time-2.3.3.tgz", "integrity": "sha512-SsjjOPHl/FfrTQNgmc5oen8Hr1Jxpn6LlHNXxCIFdYMHuK1kMeYMobb9XN3mvxaGQm3dbegqYFMX4+GDORfbWg==", "license": "MIT", "dependencies": {"depd": "~2.0.0", "on-headers": "~1.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/restructure": {"version": "3.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/restructure/-/restructure-3.0.2.tgz", "integrity": "sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==", "license": "MIT"}, "node_modules/retry": {"version": "0.13.1", "resolved": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "integrity": "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/reusify/-/reusify-1.1.0.tgz", "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/rfdc/-/rfdc-1.3.0.tgz", "integrity": "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==", "license": "MIT"}, "node_modules/rimraf": {"version": "6.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/rimraf/-/rimraf-6.0.1.tgz", "integrity": "sha512-9dkvaxAsk/xNXSJzMgFqqMCuFgt2+KsOFek3TMLfo8NCPfWpBmqwyNn5Y+NX56QUYfCtsyhF3ayiboEoUmJk/A==", "dev": true, "license": "ISC", "dependencies": {"glob": "^11.0.0", "package-json-from-dist": "^1.0.0"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/rimraf/node_modules/glob": {"version": "11.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/glob/-/glob-11.0.0.tgz", "integrity": "sha512-9UiX/Bl6J2yaBbxKoEBRm4Cipxgok8kQYcOPEhScPwebu2I0HoQOuYdIO6S3hLuWoZgpDpwQZMzTFxgpkyT76g==", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^4.0.1", "minimatch": "^10.0.0", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^2.0.0"}, "bin": {"glob": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/minimatch": {"version": "10.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/minimatch/-/minimatch-10.0.1.tgz", "integrity": "sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/router": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/router/-/router-2.2.0.tgz", "integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/router/node_modules/debug": {"version": "4.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-array-concat/-/safe-array-concat-1.0.1.tgz", "integrity": "sha512-6XbUAseYE2KtOuGueyeobCySj9L4+66Tn6KQMOPQJrAJEowYKW/YR/MGJZl7FdydUdaFu4LYyDZjxf4/Nmo23Q==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "has-symbols": "^1.0.3", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-array-concat/node_modules/isarray": {"version": "2.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/isarray/-/isarray-2.0.5.tgz", "integrity": "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==", "license": "MIT", "optional": true}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safe-regex-test/-/safe-regex-test-1.0.0.tgz", "integrity": "sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}}, "node_modules/safe-stable-stringify": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.4.3.tgz", "integrity": "sha512-e2bDA2WJT0wxseVd4lsDP4+3ONX6HpMXQa1ZhFQ7SU+GjvORCmShbCMltrtIDfkYhVHrOcPtj+KhmDBdPdZD1g==", "engines": {"node": ">=10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/secure-json-parse": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/secure-json-parse/-/secure-json-parse-2.7.0.tgz", "integrity": "sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw=="}, "node_modules/semver": {"version": "7.6.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/semver/-/semver-7.6.3.tgz", "integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/send/-/send-1.2.0.tgz", "integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/send/node_modules/fresh": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/fresh/-/fresh-2.0.0.tgz", "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/send/node_modules/mime-db": {"version": "1.54.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/send/node_modules/mime-types": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "integrity": "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/set-function-name/-/set-function-name-2.0.1.tgz", "integrity": "sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==", "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.0.1", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/shallow-clone-shim": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shallow-clone-shim/-/shallow-clone-shim-2.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>diL3KGOoS67d73TEmk4tdPTO9GSMCoiphQsTcC9EtC+AOmMPjkyBkRoCJfW9ASsaZw1craaiw1dPN2D3Q==", "optional": true}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-update-notifier": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "integrity": "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/sinon": {"version": "21.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/sinon/-/sinon-21.0.0.tgz", "integrity": "sha512-TOgRcwFPbfGtpqvZw+hyqJDvqfapr1qUlOizROIk4bBLjlsjlB00Pg6wMFXNtJRpu+eCZuVOaLatG7M8105kAw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.1", "@sinonjs/fake-timers": "^13.0.5", "@sinonjs/samsam": "^8.0.1", "diff": "^7.0.0", "supports-color": "^7.2.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/sinon"}}, "node_modules/socket.io": {"version": "4.8.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/socket.io/-/socket.io-4.8.1.tgz", "integrity": "sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg==", "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "~2.0.0", "cors": "~2.8.5", "debug": "~4.3.2", "engine.io": "~6.6.0", "socket.io-adapter": "~2.5.2", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.2.0"}}, "node_modules/socket.io-adapter": {"version": "2.5.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/socket.io-adapter/-/socket.io-adapter-2.5.5.tgz", "integrity": "sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg==", "license": "MIT", "dependencies": {"debug": "~4.3.4", "ws": "~8.17.1"}}, "node_modules/socket.io-parser": {"version": "4.2.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/socket.io-parser/-/socket.io-parser-4.2.4.tgz", "integrity": "sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==", "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/sonic-boom": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/sonic-boom/-/sonic-boom-1.4.1.tgz", "integrity": "sha512-LRHh/A8tpW7ru89lrlkU4AszXt1dbwSjVWguGrmlxE7tawVmDBlI1PILMkXAxJTwqhgsEeTHzj36D5CmHgQmNg==", "optional": true, "dependencies": {"atomic-sleep": "^1.0.0", "flatstr": "^1.0.12"}}, "node_modules/sprintf-js": {"version": "1.1.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sql-summary": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sql-summary/-/sql-summary-1.0.1.tgz", "integrity": "sha512-IpCr2tpnNkP3Jera4ncexsZUp0enJBLr+pHCyTweMUBrbJsTgQeLWx1FXLhoBj/MvcnUQpkgOn2EY8FKOkUzww==", "optional": true}, "node_modules/stack-trace": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha512-KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==", "engines": {"node": "*"}}, "node_modules/stackframe": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz", "integrity": "sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==", "optional": true}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stoppable": {"version": "1.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/stoppable/-/stoppable-1.1.0.tgz", "integrity": "sha512-KXDYZ9dszj6bzvnEMRYvxgeTHU74QBFL54XKtP3nyMuJ81CFYtABZ3bAzL2EdFUaEwJOBOgENyFj3R7oTzDyyw==", "license": "MIT", "engines": {"node": ">=4", "npm": ">=6"}}, "node_modules/stream-chopper": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/stream-chopper/-/stream-chopper-3.0.1.tgz", "integrity": "sha512-f7h+ly8baAE26iIjcp3VbnBkbIRGtrvV0X0xxFM/d7fwLTYnLzDPTXRKNxa2HZzohOrc96NTrR+FaV3mzOelNA==", "optional": true, "dependencies": {"readable-stream": "^3.0.6"}}, "node_modules/stream-chopper/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "optional": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/stream-chopper/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "optional": true}, "node_modules/stream-chopper/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "optional": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "license": "MIT"}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.trim": {"version": "1.2.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string.prototype.trim/-/string.prototype.trim-1.2.8.tgz", "integrity": "sha512-lfjY4HcixfQXOfaqCvcBuOIapyaroTXhbkfJN3gcB1OtyupngWK4sEET9Knd0cXd28kTUqu/kHoV4HKSJdnjiQ==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string.prototype.trimend/-/string.prototype.trimend-1.0.7.tgz", "integrity": "sha512-Ni79DqeB72ZFq1uH/L6zJ+DKZTkOtPIHovb3YZHQViE+HDouuU4mBrLOLDn5Dde3RF8qw5qVETEjhu9locMLvA==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.7", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string.prototype.trimstart/-/string.prototype.trimstart-1.0.7.tgz", "integrity": "sha512-NGhtDFu3jCEm7B4Fy0DpLewdJQOZcQ0rGbwQ/+stjnrp2i+rlKeCvos9hOIeCmqwratM47OBxY7uFZzjxHXmrg==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-color": {"version": "0.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/strip-color/-/strip-color-0.1.0.tgz", "integrity": "sha512-p9LsUieSjWNNAxVCXLeilaDlmuUOrDS5/dF9znM1nZc7EGX5+zEFC0bEevsNIaldjlks+2jns5Siz6F9iK6jwA==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/tarn": {"version": "3.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/tarn/-/tarn-3.0.2.tgz", "integrity": "sha512-51LAVKUSZSVfI05vjPESNc5vwqqZpbXCsU+/+wxlOrUjk2SnFTt97v9ZgQrD4YmxYW1Px6w2KjaDitCfkvgxMQ==", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/tdigest": {"version": "0.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/tdigest/-/tdigest-0.1.2.tgz", "integrity": "sha512-+G0LLgjjo9BZX2MfdvPfH+MKLCrxlXSYec5DaPYP1fe6Iyhf0/fSmJ0bFiZ1F8BT6cGXl2LpltQptzjXKWEkKA==", "license": "MIT", "dependencies": {"bintrees": "1.0.2"}}, "node_modules/tedious": {"version": "18.2.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/tedious/-/tedious-18.2.1.tgz", "integrity": "sha512-DKsTgGBC0ZeZexAd5OObfeKd0Tlx3jx3kNoKImsxfBKdRuV216u9n6Sr+4w6vzn+S4r43XmWAXQwM7UkDkbIEg==", "license": "MIT", "dependencies": {"@azure/identity": "^4.2.1", "@azure/keyvault-keys": "^4.4.0", "@js-joda/core": "^5.6.1", "@types/node": ">=18", "bl": "^6.0.11", "iconv-lite": "^0.6.3", "js-md4": "^0.3.2", "native-duplexpair": "^1.0.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">=18"}}, "node_modules/text-hex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/text-hex/-/text-hex-1.0.0.tgz", "integrity": "sha512-uuVGNWzgJ4yhRaNSiubPY7OjISw4sw4E5Uv0wbjp+OzcbmVU/rsT8ujgcXJhn9ypzsgr5vlzpPqP+MBBKcGvbg=="}, "node_modules/through2": {"version": "2.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/through2/-/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/through2/node_modules/isarray": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "license": "MIT"}, "node_modules/through2/node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/through2/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/tiny-inflate": {"version": "1.0.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/tiny-inflate/-/tiny-inflate-1.0.3.tgz", "integrity": "sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/to-source-code": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/to-source-code/-/to-source-code-1.0.2.tgz", "integrity": "sha512-YzWtjmNIf3E75eZYa7m1SCyl0vgOGoTzdpH3svfa8SUm5rqTgl9hnDolrAGOghCF9P2gsITXQoMrlujOoz+RPw==", "optional": true, "dependencies": {"is-nil": "^1.0.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/touch": {"version": "3.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/touch/-/touch-3.1.0.tgz", "integrity": "sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==", "dev": true, "license": "ISC", "dependencies": {"nopt": "~1.0.10"}, "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tr46": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz", "integrity": "sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==", "optional": true, "dependencies": {"punycode": "^2.1.0"}}, "node_modules/triple-beam": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/triple-beam/-/triple-beam-1.4.1.tgz", "integrity": "sha512-aZbgViZrg1QNcG+LULa7nhZpJTZSLm/mXnHXnbAbjmN5aSa0y7V+wvv6+4WaBtpISJzThKy+PIPxc1Nq1EJ9mg==", "engines": {"node": ">= 14.0.0"}}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "integrity": "sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-mocha": {"version": "11.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ts-mocha/-/ts-mocha-11.1.0.tgz", "integrity": "sha512-yT7FfzNRCu8ZKkYvAOiH01xNma/vLq6Vit7yINKYFNVP8e5UyrYXSOMIipERTpzVKJQ4Qcos5bQo1tNERNZevQ==", "dev": true, "license": "MIT", "bin": {"ts-mocha": "bin/ts-mocha"}, "engines": {"node": ">= 6.X.X"}, "peerDependencies": {"mocha": "^3.X.X || ^4.X.X || ^5.X.X || ^6.X.X || ^7.X.X || ^8.X.X || ^9.X.X || ^10.X.X || ^11.X.X", "ts-node": "^7.X.X || ^8.X.X || ^9.X.X || ^10.X.X", "tsconfig-paths": "^4.X.X"}, "peerDependenciesMeta": {"tsconfig-paths": {"optional": true}}}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ts-node/-/ts-node-10.9.2.tgz", "integrity": "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/ts-node/node_modules/diff": {"version": "4.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/diff/-/diff-4.0.2.tgz", "integrity": "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typed-array-buffer/-/typed-array-buffer-1.0.0.tgz", "integrity": "sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typed-array-byte-length/-/typed-array-byte-length-1.0.0.tgz", "integrity": "sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typed-array-byte-offset/-/typed-array-byte-offset-1.0.0.tgz", "integrity": "sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==", "license": "MIT", "optional": true, "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.4", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typed-array-length/-/typed-array-length-1.0.4.tgz", "integrity": "sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.34.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/typescript-eslint/-/typescript-eslint-8.34.1.tgz", "integrity": "sha512-XjS+b6Vg9oT1BaIUfkW3M3LvqZE++rbzAMEHuccCfO/YkP43ha6w3jTEMilQxMF92nVOYCcdjv1ZUhAa1D/0ow==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "8.34.1", "@typescript-eslint/parser": "8.34.1", "@typescript-eslint/utils": "8.34.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/unbox-primitive": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/unbox-primitive/-/unbox-primitive-1.0.2.tgz", "integrity": "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=", "license": "MIT", "optional": true, "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}}, "node_modules/undefsafe": {"version": "2.0.5", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/undefsafe/-/undefsafe-2.0.5.tgz", "integrity": "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==", "dev": true, "license": "MIT"}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "license": "MIT"}, "node_modules/unicode-byte-truncate": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unicode-byte-truncate/-/unicode-byte-truncate-1.0.0.tgz", "integrity": "sha512-GQgHk6DodEoKddKQdjnv7xKS9G09XCfHWX0R4RKht+EbUMSiVEmtWHGFO8HUm+6NvWik3E2/DG4MxTitOLL64A==", "optional": true, "dependencies": {"is-integer": "^1.0.6", "unicode-substring": "^0.1.0"}}, "node_modules/unicode-properties": {"version": "1.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/unicode-properties/-/unicode-properties-1.4.1.tgz", "integrity": "sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==", "license": "MIT", "dependencies": {"base64-js": "^1.3.0", "unicode-trie": "^2.0.0"}}, "node_modules/unicode-substring": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/unicode-substring/-/unicode-substring-0.1.0.tgz", "integrity": "sha512-36Xaw9wXi7MB/3/EQZZHkZyyiRNa9i3k9YtPAz2KfqMVH2xutdXyMHn4Igarmnvr+wOrfWa/6njhY+jPpXN2EQ==", "optional": true}, "node_modules/unicode-trie": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/unicode-trie/-/unicode-trie-2.0.0.tgz", "integrity": "sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==", "license": "MIT", "dependencies": {"pako": "^0.2.5", "tiny-inflate": "^1.0.0"}}, "node_modules/uninstall": {"version": "0.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uninstall/-/uninstall-0.0.0.tgz", "integrity": "sha512-pjP/0+A4gsbDVa8XH/S2GZdT9NPJW8NFMy3GI7HnsWG+NAmFSSj3QidNosXBI9cPtxxNExEDdhKFO6sli8K3mA==", "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/untildify": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/untildify/-/untildify-4.0.0.tgz", "integrity": "sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse": {"version": "1.5.10", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/url-parse/-/url-parse-1.5.10.tgz", "integrity": "sha1-nTwvc2wddd070r5QfcwRHx4uqcE=", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/uuid": {"version": "11.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/uuid/-/uuid-11.1.0.tgz", "integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==", "dev": true, "license": "MIT"}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/v8-to-istanbul/node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true, "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/webidl-conversions": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz", "integrity": "sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==", "optional": true}, "node_modules/whatwg-url": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz", "integrity": "sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==", "optional": true, "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "integrity": "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=", "license": "MIT", "optional": true, "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}}, "node_modules/which-typed-array": {"version": "1.1.13", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/which-typed-array/-/which-typed-array-1.1.13.tgz", "integrity": "sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==", "license": "MIT", "optional": true, "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.4", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/winston": {"version": "3.17.0", "resolved": "https://registry.npmjs.org/winston/-/winston-3.17.0.tgz", "integrity": "sha512-DLiFIXYC5fMPxaRg832S6F5mJYvePtmO5G9v9IgUFPhXm9/GkXarH/TUrBAVzhTCzAj9anE/+GjrgXp/54nOgw==", "license": "MIT", "dependencies": {"@colors/colors": "^1.6.0", "@dabh/diagnostics": "^2.0.2", "async": "^3.2.3", "is-stream": "^2.0.0", "logform": "^2.7.0", "one-time": "^1.0.0", "readable-stream": "^3.4.0", "safe-stable-stringify": "^2.3.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.9.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/winston-elasticsearch": {"version": "0.16.1", "resolved": "https://registry.npmjs.org/winston-elasticsearch/-/winston-elasticsearch-0.16.1.tgz", "integrity": "sha512-IZ7NWs+tBYSRf9CKYs0xmS42IIfyKnd+miWrGl58WLfDLBphIhqwol6im5dUcW2xjMCP4hSovaqipAgzC5MCYQ==", "dependencies": {"@elastic/elasticsearch": "^7.16.0", "dayjs": "^1.10.7", "debug": "^4.3.3", "lodash.defaults": "^4.2.0", "lodash.omit": "^4.5.0", "promise": "^8.1.0", "retry": "^0.13.1", "winston": "^3.4.0", "winston-transport": "^4.4.2"}, "engines": {"node": ">= 8.0.0"}, "optionalDependencies": {"elastic-apm-node": "^3.20.0"}}, "node_modules/winston-transport": {"version": "4.9.0", "resolved": "https://registry.npmjs.org/winston-transport/-/winston-transport-4.9.0.tgz", "integrity": "sha512-8drMJ4rkgaPo1Me4zD/3WLfI/zPdA9o2IipKODunnGDcuqbHwjsbB79ylv04LCGGzU0xQ6vTznOMpQGaLhhm6A==", "license": "MIT", "dependencies": {"logform": "^2.7.0", "readable-stream": "^3.6.2", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/winston-transport/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/winston-transport/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/winston-transport/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/winston/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/winston/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/winston/node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/workerpool": {"version": "9.3.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/workerpool/-/workerpool-9.3.2.tgz", "integrity": "sha512-Xz4Nm9c+LiBHhDR5bDLnNzmj6+5F+cyEAWPMkbs2awq/dYazR/efelZzUAjB/y3kNHL+uzkHvxVVpaOfGCPV7A==", "dev": true, "license": "Apache-2.0"}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/ws": {"version": "8.18.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/ws/-/ws-8.18.0.tgz", "integrity": "sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/xml/-/xml-1.0.1.tgz", "integrity": "sha512-huCv9IH9Tcf95zuYCsQraZtWnJvBtLVE0QHMOs8bWyZAFZNDcYjsPq1nEx8jKA9y+Beo9v+7OBPRisQTjinQMw==", "dev": true, "license": "MIT"}, "node_modules/xmlbuilder": {"version": "15.1.1", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "integrity": "sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==", "engines": {"node": ">=8.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "16.2.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yargs/-/yargs-16.2.0.tgz", "integrity": "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs-unparser": {"version": "2.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yargs-unparser/-/yargs-unparser-2.0.0.tgz", "integrity": "sha512-7pRTIA9Qc1caZ0bZ6RYRGbHJthJWuakf+WmHK0rVeLkNrrGhfoabBNdue6kdINI6r4if7ocq9aD/n7xwKOdzOA==", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^6.0.0", "decamelize": "^4.0.0", "flat": "^5.0.2", "is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/yargs-unparser/node_modules/camelcase": {"version": "6.3.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/yargs-unparser/node_modules/decamelize": {"version": "4.0.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/decamelize/-/decamelize-4.0.0.tgz", "integrity": "sha512-9iE1PgSik9HeIIw2JO94IidnE3eBoQrFJ3w7sFuzSX4DpmZ3v5sZpUiV5Swcf6mQEF+Y0ru8Neo+p+nyh2J+hQ==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yn/-/yn-3.1.1.tgz", "integrity": "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zod": {"version": "3.25.67", "resolved": "https://nexus-repository.northgrum.com/repository/npm-ng-group/zod/-/zod-3.25.67.tgz", "integrity": "sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}