import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextResources, LearningContextResourcesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-resources.js'

export default async function getResourcesByLearningContextID (contextID: string): Promise<LearningContextResources[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextResources>(LearningContextResourcesTableName, pool.request(), { LearningContextID: contextID })
  return records
}